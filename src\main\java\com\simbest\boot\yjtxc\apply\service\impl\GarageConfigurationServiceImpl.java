package com.simbest.boot.yjtxc.apply.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.datapermission.tools.BelongInfoTool;
import com.simbest.boot.sys.model.UploadFileResponse;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.sys.service.impl.SysFileService;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.yjtxc.apply.model.*;
import com.simbest.boot.yjtxc.apply.repository.GarageConfigurationRepository;
import com.simbest.boot.yjtxc.apply.service.*;
import com.simbest.boot.yjtxc.util.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.annotation.Transient;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.persistence.criteria.Predicate;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/5/30 14:30
 * @describe 应急车库配置业务实现层
 */
@Slf4j
@Service
public class GarageConfigurationServiceImpl extends LogicService<GarageConfiguration, String> implements IGarageConfigurationService {

    private GarageConfigurationRepository repository;

    public GarageConfigurationServiceImpl(GarageConfigurationRepository repository) {
        super(repository);
        this.repository = repository;
    }

    @Autowired
    private ISysOperateLogService sysOperateLogService;

    @Autowired
    private IApplicationFormService iApplicationFormService;


    @Autowired
    private IEmergencyEquipmentService iEmergencyEquipmentService;

    @Autowired
    private SysFileService fileService;

    @Autowired
    private IFiveBaseStationService iFiveBaseStationService;

    @Autowired
    private IFourFDDBaseStationService iFourFDDBaseStationService;

    @Autowired
    private IFourTDDBaseStationService iFourTDDBaseStationService;


    final String param1 = "action/garageConfiguration/";

    /**
     * 导入应急车库配置信息
     *
     * @param request
     * @param response
     * @return
     */
    @Override
    public JsonResponse importPerson(HttpServletRequest request, HttpServletResponse response) {
        JsonResponse jsonResponse = JsonResponse.defaultSuccessResponse();
        response.setContentType("text/html; charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        List<GarageConfiguration> garageConfigurationList = null;
        try {
            PrintWriter out = response.getWriter();
            MultipartHttpServletRequest mureq = (MultipartHttpServletRequest) request;
            Map<String, MultipartFile> multipartFiles = mureq.getFileMap();
            for (MultipartFile uploadfile : multipartFiles.values()) {
                /**
                 * 这里要区分是“外置设备”还是“电脑终端设备”,处理之后返回最终的设备信息集合
                 */
                String originalFilename = uploadfile.getOriginalFilename();
                // 如果为电脑终端设备
                if (StringUtils.isNotEmpty(originalFilename) && originalFilename.contains("xls")) {
                    // 先上传至sys_file表,注意sheetName名要与excel保持一致
                    UploadFileResponse uploadFileResponse = fileService.importExcel(uploadfile, request.getParameter("pmInsType"),
                            request.getParameter("pmInsId"), request.getParameter("pmInsTypePart"), GarageConfiguration.class,
                            "应急车配置模板导入");
                    jsonResponse.setData(uploadFileResponse);
                    jsonResponse.setMessage("上传成功");
                } else {
                    jsonResponse.setMessage("请按照模板上传相关数据!");
                }
            }
            String result = "<script type=\"text/javascript\">parent.result=" + JacksonUtils.obj2json(jsonResponse) + "</script>";
            out.println(result);
            out.close();
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return JsonResponse.success(garageConfigurationList);

    }

    /**
     * 保存应急车库配置信息
     *
     * @param garageConfigurationList
     * @return
     */
    @Override
    public JsonResponse saveGarageConfiguration(List<GarageConfiguration> garageConfigurationList) {
        Map<String, Object> optLogParam = Maps.newHashMap();
        optLogParam.put("source", Constants.PC);
        optLogParam.put("GarageConfiguration", garageConfigurationList);
        optLogParam.put("operateInterface", param1 + "saveGarageConfiguration");
        try {
            List<GarageConfiguration> linkedList = CollectionUtil.newLinkedList();
            Iterator<GarageConfiguration> iterator = garageConfigurationList.iterator();
            while (iterator.hasNext()) {
                GarageConfiguration garageConfiguration = iterator.next();
                garageConfiguration.setPmInsId(createNumber());
                BelongInfoTool.setBelongCompanyAndDepartment(garageConfiguration);
                garageConfiguration.setSchedulingCondition("可调度");
                // 根据实际导入的数据设置应急设备大类和子类，不再硬编码
                // garageConfiguration.setCarConfiguRationParent("应急车"); // 应急设备大类
                // garageConfiguration.setCarConfiguRation("应急通讯车"); // 应急设备子类
                linkedList.add(garageConfiguration);
                insert(garageConfiguration);
            }
            //saveAll(linkedList);
        } catch (Exception e) {
            Exceptions.printException(e);
            optLogParam.put("errorMsg", e.getMessage());
        } finally {
            sysOperateLogService.saveLog(optLogParam);
        }
        return JsonResponse.defaultSuccessResponse();
    }

    /**
     * 删除应急车库配置信息
     *
     * @param id
     * @return
     */
    //@Transactional
    @Override
    public JsonResponse deleteGarageConfiguration(String id) {
        Map<String, Object> optLogParam = Maps.newHashMap();
        optLogParam.put("source", Constants.PC);
        optLogParam.put("id", id);
        optLogParam.put("operateInterface", param1 + "deleteGarageConfiguration");
        try {
            GarageConfiguration garageConfiguration = findById(id);
            String pmInsId = garageConfiguration.getPmInsId();
            deleteById(id);
            //删除5G基站站号关联应急车库配置信息
            iFiveBaseStationService.deleteByFiveBaseStationPmInsId(pmInsId);
            //删除4GFDD基站站号关联应急车库配置信息
            iFourFDDBaseStationService.deleteByFourFDDBaseStationPmInsId(pmInsId);
            //删除4GTDD基站站号关联应急车库配置信息
            iFourTDDBaseStationService.deleteByFourTDDBaseStationServicePmInsId(pmInsId);
        } catch (Exception e) {
            Exceptions.printException(e);
            optLogParam.put("errorMsg", e.getMessage());
        } finally {
            sysOperateLogService.saveLog(optLogParam);
        }
        return JsonResponse.defaultSuccessResponse();
    }

    /**
     * 停用||启用、车库配置信息
     *
     * @param id
     * @param flag
     * @return
     */
    //@Transactional
    @Override
    public JsonResponse updateGarageConfiguration(String id, String flag) {
        Map<String, Object> optLogParam = Maps.newHashMap();
        optLogParam.put("source", Constants.PC);
        optLogParam.put("id", id);
        optLogParam.put("flag", flag);
        optLogParam.put("operateInterface", param1 + "updateGarageConfiguration");
        try {
            GarageConfiguration garageConfiguration = findById(id);
            //应急车车牌
            String licensePlate = garageConfiguration.getLicensePlate();
            //获取所属地市
            String cities = garageConfiguration.getCities();
            Boolean enable = iEmergencyEquipmentService.findByGarageConfiguration(licensePlate, cities);
            if (!enable) {
                return JsonResponse.fail("车辆正在被使用,停用失败");
            }
            repository.updateGarageConfigurationSchedulingCondition(flag, id);
        } catch (Exception e) {
            Exceptions.printException(e);
            optLogParam.put("errorMsg", e.getMessage());
            return JsonResponse.fail("数据更新失败");
        } finally {
            sysOperateLogService.saveLog(optLogParam);
        }
        return JsonResponse.success("数据更新成功");
    }

    /**
     * 条件查询车库配置信息
     *
     * @param source
     * @param pageable
     * @param currentUserCode
     * @param paramMap
     * @return
     */
    @Override
    public JsonResponse findByAllGarageConfiguration(String source, Pageable pageable, String currentUserCode, Map<String, Object> paramMap) {
        Page<GarageConfiguration> findAllGarageConfiguration = null;
        Map<String, Object> optLogParam = Maps.newHashMap();
        optLogParam.put("source", Constants.PC);
        optLogParam.put("paramMap", paramMap);
        optLogParam.put("operateInterface", param1 + "findByAllGarageConfiguration");
        try {
            //获取地市
            String cities = MapUtil.getStr(paramMap, "cities");
            //获取设备厂家
            String equipmentManufacturer = MapUtil.getStr(paramMap, "equipmentManufacturer");
            //获取应急车配置子类
            String carConfiguRation = MapUtil.getStr(paramMap, "carConfiguRation");
            //获取应急车配置父类
            String carConfiguRationParent = MapUtil.getStr(paramMap, "carConfiguRationParent");
            //获取应急车车牌号
            String licensePlate = MapUtil.getStr(paramMap, "licensePlate");
            //获取调度状态
            String schedulingCondition = MapUtil.getStr(paramMap, "schedulingCondition");
            Specification<GarageConfiguration> specification = (root, query, criteriaBuilder) -> {
                List<Predicate> predicateList = Lists.newArrayList();
                if (StrUtil.isNotEmpty(cities)) {
                    predicateList.add(criteriaBuilder.like(root.get("cities"), "%" + cities + "%"));
                }
                if (StrUtil.isNotEmpty(equipmentManufacturer)) {
                    predicateList.add(criteriaBuilder.like(root.get("equipmentManufacturer"), "%" + equipmentManufacturer + "%"));
                }
                if (StrUtil.isNotEmpty(carConfiguRation)) {
                    predicateList.add(criteriaBuilder.like(root.get("carConfiguRation"), "%" + carConfiguRation + "%"));
                }
                if (StrUtil.isNotEmpty(carConfiguRationParent)) {
                    predicateList.add(criteriaBuilder.like(root.get("carConfiguRationParent"), "%" + carConfiguRationParent + "%"));
                }
                if (true) {
                    predicateList.add(criteriaBuilder.equal(root.get("enabled"), true));
                }
                if (StrUtil.isNotEmpty(licensePlate)) {
                    predicateList.add(criteriaBuilder.like(root.get("licensePlate").as(String.class), "%" + licensePlate + "%"));
                }
                if (StrUtil.isNotEmpty(schedulingCondition)) {
                    predicateList.add(criteriaBuilder.equal(root.get("schedulingCondition").as(String.class), schedulingCondition));
                }
                Predicate[] predicates = new Predicate[predicateList.size()];
                return criteriaBuilder.and(predicateList.toArray(predicates));
            };
            findAllGarageConfiguration = repository.findAll(specification, pageable);
        } catch (Exception e) {
            Exceptions.printException(e);
            optLogParam.put("errorMsg", e.getMessage());
        } finally {
            sysOperateLogService.saveLog(optLogParam);
        }
        return JsonResponse.success(findAllGarageConfiguration);
    }

    /**
     * 查询大型应急车
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> findByBigCar() {

        return repository.findByBigCar();
    }

    /**
     * 查询GSM卫星车
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> findByGSMCar() {
        return repository.findByGSMCar();
    }

    /**
     * 大型应急车按照地市条件查询
     *
     * @param cities
     * @return
     */
    @Override
    public List<Map<String, Object>> findByBigCarAndCities(String cities) {
        return repository.findByBigCarAndCities(cities);
    }

    /**
     * GSM卫星车按照地市条件查询
     *
     * @param cities
     * @return
     */
    @Override
    public List<Map<String, Object>> findByGSMCarAndCities(String cities) {
        return repository.findByGSMCarAndCities(cities);
    }

    @Override
    public List<GarageConfiguration> findByIdddd(String phoneNmu ,String vehicle) {
        return repository.findByIdddd(phoneNmu ,vehicle);
    }

    @Override
    public GarageConfiguration findByPonte(String phoneNmu) {
        return repository.findByPonte(phoneNmu);
    }

    @Override
    public List<GarageConfiguration> findByPonteList(String phoneNmu) {
        return repository.findByPonteList(phoneNmu);
    }

    /**
     * 根据车牌号和手机查询
     * @param phoneNmu
     * @param protaion
     * @return
     */
    @Override
    public GarageConfiguration findByPonteLiattion(String phoneNmu, String protaion) {
        return repository.findByPonteLiattion(phoneNmu,protaion);
    }

    /**
     * 生成工单编号
     *
     * @return
     */
    public String createNumber() {
        List<GarageConfiguration> lists = repository.findAll();
        int length = lists.size();
        SimpleDateFormat sdf = new SimpleDateFormat("YYYYMMDD");
        String dateValue = sdf.format(new Date());
        String number = "0001";
        String num = String.valueOf(length + 1);
        if (length == 0) {
            number = dateValue + "0001";
        } else if (length > 0 && length < 10) {
            number = dateValue + "000" + num;
        } else if (length >= 10 && length < 100) {
            number = dateValue + "00" + num;
        } else if (length >= 100 && length < 1000) {
            number = dateValue + "0" + num;
        } else {
            number = dateValue + num;
        }
        return number;

    }

    /**
     * 获取所有不重复的应急设备大类列表
     *
     * @return JsonResponse包含应急设备大类列表
     */
    @Override
    public JsonResponse getCarConfiguRationParentList() {
        Map<String, Object> optLogParam = Maps.newHashMap();
        optLogParam.put("source", Constants.PC);
        optLogParam.put("operateInterface", param1 + "getCarConfiguRationParentList");
        try {
            List<String> carConfiguRationParentList = repository.findDistinctCarConfiguRationParent();
            return JsonResponse.success(carConfiguRationParentList);
        } catch (Exception e) {
            Exceptions.printException(e);
            optLogParam.put("errorMsg", e.getMessage());
            return JsonResponse.fail("获取应急设备大类列表失败");
        } finally {
            sysOperateLogService.saveLog(optLogParam);
        }
    }

    @Override
    public JsonResponse getCarConfiguRationList() {
        Map<String, Object> optLogParam = Maps.newHashMap();
        optLogParam.put("source", Constants.PC);
        optLogParam.put("operateInterface", param1 + "getCarConfiguRationList");
        try {
            List<String> carConfiguRationParentList = repository.findDistinctCarConfiguRation();
            return JsonResponse.success(carConfiguRationParentList);
        } catch (Exception e) {
            Exceptions.printException(e);
            optLogParam.put("errorMsg", e.getMessage());
            return JsonResponse.fail("获取应急设备子类列表失败");
        } finally {
            sysOperateLogService.saveLog(optLogParam);
        }
    }
}
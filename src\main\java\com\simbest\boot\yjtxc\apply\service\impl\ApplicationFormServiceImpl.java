package com.simbest.boot.yjtxc.apply.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.simbest.boot.base.exception.Exceptions;
import com.simbest.boot.base.repository.CustomDynamicWhere;
import com.simbest.boot.base.service.impl.LogicService;
import com.simbest.boot.base.web.response.JsonResponse;
import com.simbest.boot.bps.common.WfParticipantOperator;
import com.simbest.boot.bps.process.bussiness.model.ActBusinessStatus;
import com.simbest.boot.bps.process.bussiness.service.IActBusinessStatusService;
import com.simbest.boot.bps.process.listener.model.WFNotificationInstModel;
import com.simbest.boot.bps.process.listener.model.WfWorkItemModel;
import com.simbest.boot.constants.ApplicationConstants;
import com.simbest.boot.datapermission.common.service.IQueryLevelConfigService;
import com.simbest.boot.datapermission.constants.DataPermissionConstants;
import com.simbest.boot.datapermission.tools.BelongInfoTool;
import com.simbest.boot.datapermission.tools.DataPermissionTool;
import com.simbest.boot.security.*;
import com.simbest.boot.security.auth.service.IAuthUserCacheService;
import com.simbest.boot.sys.model.SysFile;
import com.simbest.boot.sys.model.SysOperateLog;
import com.simbest.boot.sys.service.ISysOperateLogService;
import com.simbest.boot.templates.MessageEnum;
import com.simbest.boot.util.ObjectUtil;
import com.simbest.boot.util.json.JacksonUtils;
import com.simbest.boot.util.office.ExcelUtil;
import com.simbest.boot.util.security.SecurityUtils;
import com.simbest.boot.uums.api.app.UumsSysAppDecisionApi;
import com.simbest.boot.uums.api.org.UumsSysOrgApi;
import com.simbest.boot.uums.api.user.UumsSysUserinfoApi;
import com.simbest.boot.wf.process.service.IProcessInstanceService;
import com.simbest.boot.wf.process.service.IWFNotificationService;
import com.simbest.boot.wf.process.service.IWfOptMsgService;
import com.simbest.boot.wf.process.service.IWorkItemService;
import com.simbest.boot.yjtxc.apply.dto.ExportOrder;
import com.simbest.boot.yjtxc.apply.model.ApplicationForm;
import com.simbest.boot.yjtxc.apply.model.EmergencyEquipment;
import com.simbest.boot.yjtxc.apply.model.GarageConfiguration;
import com.simbest.boot.yjtxc.apply.repository.ApplicationFormRepository;
import com.simbest.boot.yjtxc.apply.service.*;
import com.simbest.boot.yjtxc.attachment.service.IFileExtendService;
import com.simbest.boot.yjtxc.mainbills.model.UsPmInstence;
import com.simbest.boot.yjtxc.mainbills.service.IUsPmInstenceService;
import com.simbest.boot.yjtxc.todo.TodoBusOperatorService;
import com.simbest.boot.yjtxc.util.*;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;


/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2022/6/1 9:34
 * @describe 日常应急-地市内调动
 */
@Slf4j
@Service
public class ApplicationFormServiceImpl extends LogicService<ApplicationForm, String> implements IApplicationFormService {

    private ApplicationFormRepository applicationFormRepository;

    public static final int SUCCESS_CODE = 0;
    public static final int ERROR_CODE = -1;
    public static final int SUCCESS_STATUS = 200;
    public static final int ERROR_STATUS = 500;

    @Autowired
    private TodoBusOperatorService todoBusOperatorService;

    public ApplicationFormServiceImpl(ApplicationFormRepository applicationFormRepository) {
        super(applicationFormRepository);
        this.applicationFormRepository = applicationFormRepository;
    }

    @Autowired
    private IFileExtendService fileExtendService;

    @Autowired
    private IEmergencyEquipmentService iEmergencyEquipmentService;

    @Autowired
    private IuserNoSessionUtil iuserNoSessionUtil;

    @Autowired
    private ISysOperateLogService sysOperateLogService;

    @Autowired
    private CustomDynamicWhere customDynamicWhere;


    @Autowired
    private ISchedulingLedgerService iSchedulingLedgerService;
    @Autowired
    private IAuthUserCacheService authUserCacheService;

    @Autowired
    private IWFNotificationService iwfNotificationService;


    @Autowired
    private ISatellitePortableLibraryService iSatellitePortableLibraryService;

    @Autowired
    private IGarageConfigurationService iGarageConfigurationService;

    @Autowired
    private UumsSysOrgApi uumsSysOrgApi;

    @Autowired
    private IWorkItemService workItemService;

    @Autowired
    private IUavAerialConfigurationStationService iUavAerialConfigurationStationService;

    @Autowired
    private IProcessInstanceService processInstanceService;

    @Autowired
    private IActBusinessStatusService actBusinessStatusService;

    @Autowired
    private ISysOperateLogService operateLogService;

    @Autowired
    private IWorkItemService workItemManager;

    @Autowired
    private OperateLogTool operateLogTool;

    @Autowired
    private UumsSysAppDecisionApi uumsSysAppDecisionApi;

    @Autowired
    private UumsSysUserinfoApi uumsSysUserinfoApi;

    @Autowired
    private IUsPmInstenceService usPmInstenceService;

    @Autowired
    private IInvolvesPersonnelConfigurationService involvesPersonnelConfigurationService;

    @Autowired
    private IQueryLevelConfigService queryLevelConfigService;
    @Autowired
    private IGarageConfigurationService gerservice;
    @Autowired
    private IWfOptMsgService wfOptMsgService;

    @Autowired
    private IEmergencyEquipmentService emergencyEquipmentService;

    final String param1 = "/action/applicationForm/";


    /**
     * 提交下一步
     *
     * @param source          来源
     * @param currentUserCode 当前人
     * @param workItemId      活动实例id
     * @param outcome         决策连线规则
     * @param location        当前环节
     * @param copyLocation    抄送环节
     * @param bodyParam       提交审批参数
     * @param formId          表单id
     * @param notificationId  待阅表单id
     * @return
     */
    @Override
    public JsonResponse startSubmitProcess(String source, String currentUserCode, String workItemId, String outcome, String location, String copyLocation, Map<String, Object> bodyParam, String formId, String notificationId, String processInstId) {
        JsonResponse jsonResponse = new JsonResponse();
        /**处理bodyParam传来的参数**/
        if (bodyParam != null && bodyParam.size() > 0) {
            Map<String, Object> map = (Map<String, Object>) bodyParam.get("flowParam");
            if (map != null && map.size() > 0) {
                /**获取表单数据**/
                ApplicationForm form = null;
                EmergencyEquipment emergencyEquipment = null;
                Map<String, Object> formData1 = (Map<String, Object>) map.get("formData");
                //List<Map<String, Object>>  equipmentList  =  (List< Map<String, Object>>)  formData1.get("equipmentList");
                /**手机端操作时，若没有传表单数据必须传表单id**/
                if (formData1 != null && !formData1.isEmpty()) {
                    form = JacksonUtils.json2obj(JacksonUtils.obj2json(formData1), ApplicationForm.class);
                }


                if (StringUtil.isNotEmpty(formId) && StrUtil.equals(Constants.MOBILE, source)) {
                    if (form != null) {
                        String guaranteeStartTime = form.getGuaranteeStartTime();
                        String guaranteeEndTime = form.getGuaranteeEndTime();
                        if(StringUtil.isNotEmpty(guaranteeStartTime) && guaranteeStartTime.length() == 10){
                            form.setGuaranteeStartTime(guaranteeStartTime + " 00:00");
                        }
                        if(StringUtil.isNotEmpty(guaranteeEndTime) && guaranteeEndTime.length() == 10){
                            form.setGuaranteeEndTime(guaranteeEndTime + " 23:59");
                        }
                        update(form);
                    }
                    //查询附件和车辆
                    form = this.finFileAndEmergency(form, formId);

                }
                /**获取下一步审批人和抄送人**/
                List<Map<String, String>> tempList = (List<Map<String, String>>) map.get("nextUserName");
                Map<String, String> temp = new HashMap<>();
                String nextUserName = "";
                if (null != tempList && !tempList.isEmpty()) {
                    temp = tempList.get(0);
                    nextUserName = temp.get("value");
                }

                tempList = (List<Map<String, String>>) map.get("copyNextUserNames");
                String copyNextUserNames = "";
                if (null != tempList && !tempList.isEmpty()) {
                    temp = tempList.get(0);
                    copyNextUserNames = temp.get("value");
                }

                /**获取下一步审批意见和抄送意见**/
                String message = map.get("message") != null ? map.get("message").toString() : null;
                String copyMessage = map.get("copyMessage") != null ? String.valueOf(map.get("message")) : null;

                if (Constants.MOBILE.equals(source)) {
                    if (outcome.equals("yjtxc.schedulingChiefAudit_end")) {
                        nextUserName = "";
                        copyMessage = "null";
                        workItemId = null ;

                    }
                    if (outcome.equals("yjtxc.start_copy_end")) {
                        nextUserName = "";
                        copyMessage = "null";
                        workItemId = null ;
                    }
                }
                if (!Constants.OUTCOME_END.equals(outcome) && !Constants.OUTCOME_END2.equals(outcome) && StrUtil.isEmpty(nextUserName) && StrUtil.isEmpty(copyMessage)) {

                    return JsonResponse.fail("请至少选择一名人员审批");
                }
                if (form != null && form.getId() != null && StringUtils.isNotEmpty(workItemId) || StringUtils.isNotEmpty(notificationId)) {
                    jsonResponse = saveSubmitTask(form, workItemId, outcome, message, nextUserName, location, copyLocation, copyMessage, copyNextUserNames, source, currentUserCode, notificationId);
                } else {
                    jsonResponse = startProcess(form, nextUserName, outcome, message, source, currentUserCode, copyNextUserNames, copyMessage, copyLocation);
                }
            }
        }
        return jsonResponse;
    }

    /**
     * 手机端未传值时查询附件和车辆信息
     *
     * @param form
     * @param formId
     * @return
     */
    private ApplicationForm finFileAndEmergency(ApplicationForm form, String formId) {

        if (form == null) {

            form = findById(formId);

            //如果手机端传了参数
            if (ObjectUtil.isNotEmpty(form.getAttachmentList())) {
                updateFileByPmInsId(form);
            }
            List<SysFile> superviseContactListBasicFile = fileExtendService.finListSysFile(form.getPmInsId());
            //添加附件
            if (ObjectUtil.isNotEmpty(superviseContactListBasicFile) || superviseContactListBasicFile.size() != 0) {
                form.setAttachmentList(superviseContactListBasicFile);
            }

            String s1 = "大型应急车";
            String s2 = "卫星车";
            String s3 = "无人机高空站";
            String s4 = "卫星便携站";

            // 分别查询各类型设备，移除过滤条件，支持所有carConfiguRation值
            if (form.getEquipmentList() == null) {
                List<EmergencyEquipment> equipmentList = iEmergencyEquipmentService.findByPmInsIdAndD(form.getPmInsId(), s1);
                if (ObjectUtil.isNotEmpty(equipmentList) && equipmentList.size() != 0) {
                    form.setEquipmentList(equipmentList);
                }
            }

            if (form.getEquipmentMoonList() == null) {
                List<EmergencyEquipment> equipmentMoonList = iEmergencyEquipmentService.findByPmInsIdAndD(form.getPmInsId(), s2);
                if (ObjectUtil.isNotEmpty(equipmentMoonList) && equipmentMoonList.size() != 0) {
                    form.setEquipmentMoonList(equipmentMoonList);
                }
            }

            if (form.getEquipmentUavList() == null) {
                List<EmergencyEquipment> equipmentUavList = iEmergencyEquipmentService.findByPmInsIdAndD(form.getPmInsId(), s3);
                if (ObjectUtil.isNotEmpty(equipmentUavList) && equipmentUavList.size() != 0) {
                    form.setEquipmentUavList(equipmentUavList);
                }
            }

            if (form.getEquipmentBleList() == null) {
                List<EmergencyEquipment> equipmentBleList = iEmergencyEquipmentService.findByPmInsIdAndD(form.getPmInsId(), s4);
                if (ObjectUtil.isNotEmpty(equipmentBleList) && equipmentBleList.size() != 0) {
                    form.setEquipmentBleList(equipmentBleList);
                }
            }
        }
            return   form ;





}


    public JsonResponse startProcess(ApplicationForm applicationForm, String nextUserName, String outcome, String message, String source, String userCode, String copyNextUserNames, String copyMessage, String copyLocation) {
        log.debug("起草接口----------startProcess---------->" + applicationForm.toString());
        long ret = 0;
        /**准备操作日志参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/startSubmitProcess";
        String params = ",source=" + source + ",userCode=" + userCode + ",superviseContactList=" + applicationForm.toString() + ",outcome=" + outcome + ",message=" + message + ",nextUserName=" + nextUserName;
        operateLog.setInterfaceParam(params);
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            IUser iuser = SecurityUtils.getCurrentUser();
            /**校验表单和下一步审批人是否为空  **/
            if (StringUtils.isNotEmpty(nextUserName)) {
                /**获取登录人所在公司应启动的流程**/
                Map<String, String> map = this.getProcessMap(applicationForm.getProcessType());
                String processDefId = map.get("processName");
                String processType = map.get("processType");
                if (StringUtils.isNotEmpty(processDefId) && StringUtils.isNotEmpty(processType)) {
                    boolean flag = false;
                    UsPmInstence usPmInstence = new UsPmInstence();

                    String guaranteeStartTime = applicationForm.getGuaranteeStartTime();
                    String guaranteeEndTime = applicationForm.getGuaranteeEndTime();
                    if(StringUtil.isNotEmpty(guaranteeStartTime) && guaranteeStartTime.length() == 10){
                        applicationForm.setGuaranteeStartTime(guaranteeStartTime + " 00:00");
                    }
                    if(StringUtil.isNotEmpty(guaranteeEndTime) && guaranteeEndTime.length() == 10){
                        applicationForm.setGuaranteeEndTime(guaranteeEndTime + " 23:59");
                    }

                    if (applicationForm.getId() != null && applicationForm.getPmInsId() != null) {
                        this.update(applicationForm);
                        //usPmInstence = usPmInstenceService.findByPmInsId(applicationForm.getPmInsId());
                        flag = true;
                    } else {
                        usPmInstence.setPmInsType(processType);
                        /**保存业务数据**/
                        flag = this.savePlanTask(applicationForm, usPmInstence);

                    }
                    usPmInstence = usPmInstenceService.findByPmInsId(applicationForm.getPmInsId());
                    /**启动发起流程**/
                    if (flag) {
                        Map<String, Object> variables = Maps.newHashMap();
                        String currentUserCode = iuser.getUsername();
                        String currentUserName = iuser.getTruename();
                        variables.put("inputUserId", currentUserCode);
                        variables.put("receiptId", usPmInstence.getId());
                        variables.put("title", usPmInstence.getPmInsTitle());
                        variables.put("code", usPmInstence.getPmInsId());
                        variables.put("currentUserCode", currentUserCode);
                        variables.put("activityDefID", Constants.ACTIVITY_START);
                        variables.put("appCode", Constants.APP_CODE);
                        log.debug(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>参数为variables:" + variables);
                        //第一个参数为流程定义名称
                        Long workItemId = processInstanceService.startProcessAndSetRelativeData(processDefId, usPmInstence.getPmInsTitle(), usPmInstence.getPmInsTitle(), false, variables);
                        UsPmInstence pmInstence = usPmInstenceService.findByPmInsId(applicationForm.getPmInsId());//获取主单据
                        /**审批环节发起抄送  去掉**/
                        if (StringUtils.isNotEmpty(copyNextUserNames)) {//审批环节发起抄送
                            this.createToRead(String.valueOf(workItemId), pmInstence, copyNextUserNames, copyMessage, copyLocation);
                        }
                        if (workItemId != 0) {
                            /**提交表单审批处理**/
                            if (StringUtils.isNotEmpty(nextUserName)) {
                                ret = this.processApproval(workItemId, currentUserCode, currentUserName, nextUserName, outcome, Constants.ACTIVITY_START, message, usPmInstence);

                            } else {
                                operateLog.setErrorMsg("获取审批人失败");
                                JsonResponse.fail(null, "获取审批人失败");
                            }
                        } else {
                            operateLog.setErrorMsg("启动流程失败");
                            JsonResponse.fail(null, "启动流程失败");
                        }
                    } else {
                        operateLog.setErrorMsg("保存割接计划失败");
                        JsonResponse.fail(null, "保存割接计划失败");
                    }
                } else {
                    operateLog.setErrorMsg("获取流程失败");
                    JsonResponse.fail(null, "操作失败，获取流程失败!");
                }
            } else {
                operateLog.setErrorMsg("表单为空或审批人为空");
                JsonResponse.fail(null, "操作失败，请确认申请表单和审批人!");
            }
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            /**保存操作记录**/
            operateLogService.saveLog(operateLog);
        }
        String showMessage = this.getTemplate(nextUserName);
        return ret > 0 ? JsonResponse.success(ret, showMessage) : JsonResponse.fail(ret, Constants.MESSAGE_FAIL);
    }


    /**
     * 保存业务数据
     *
     * @param applicationForm 表单
     * @param usPmInstence    主单据
     * @return
     * @throws Exception
     */
    boolean savePlanTask(ApplicationForm applicationForm, UsPmInstence usPmInstence) throws Exception {
        boolean flag = false;
        IUser iuser = SecurityUtils.getCurrentUser();
        /**保存申请表单任务**/
        try {
            String pmInsId = null;

            String processType = applicationForm.getProcessType();
            switch (processType) {
                case Constants.PROCESS_TYPE_A:
                    pmInsId = creatWorkNumber(Constants.RCDDSN, processType);
                    break;
                case Constants.PROCESS_TYPE_B:
                    pmInsId = creatWorkNumber(Constants.RCDDSJ, processType);
                    break;
                default:
                    pmInsId = creatWorkNumber(Constants.ZSDD, processType);
                    break;
            }

            String title = null;
            //定义日期
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            //获取当前日期
            Date data = new Date(System.currentTimeMillis());
            String date = format.format(data);
            //保障日期
            String guaranTiem = applicationForm.getGuaranteeStartTime();
            //判断日期大小
            int res = date.compareTo(guaranTiem);
            if (res > 0 && Constants.PROCESS_TYPE_A.equals(applicationForm.getProcessType())) {
                title = Constants.PROCESS_BREATH_NAMEBS + pmInsId;
            } else {
                title = Constants.PROCESS_BREATH_NAME + pmInsId;
            }

            if (Constants.PROCESS_TYPE_C.equals(applicationForm.getProcessType())) {
                title = Constants.PROCESS_PROVINCE_NAME + pmInsId;
            }

            BelongInfoTool.setBelongCompanyAndDepartment(usPmInstence);
            usPmInstence.setPmInsId(pmInsId);
            usPmInstence.setPmInsTitle(title);
            usPmInstenceService.insert(usPmInstence);

            /**保存主单据**/
            applicationForm.setOrderTitle(title);
            applicationForm.setPmInsId(pmInsId);
            flag = this.updateApplication(applicationForm, pmInsId, Constants.ACTIVITY_START,"start"); // 保存业务数据

        } catch (Exception e) {
            flag = false;
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.debug(e.getMessage());
            return flag;
        }
        return flag;
    }


    /**
     * 更新表单附件信息
     *
     * @param applicationForm
     */
    private void updateFileByPmInsId(ApplicationForm applicationForm) {

        //获取反馈附件信息
        List<SysFile> superviseContactListBasicFile = applicationForm.getAttachmentList();
        //获取主单据ID、去关联sysFile表中的PmInsID
        String pmInsId = applicationForm.getPmInsId();
        try {
            fileExtendService.deleteByPmInsId(pmInsId);
            if (CollectionUtil.isNotEmpty(superviseContactListBasicFile)) {
                for (SysFile file : superviseContactListBasicFile) {
                    fileExtendService.updatePmInsId(pmInsId, file.getId());
                }
            }
        } catch (Exception e) {
            Exceptions.printException(e);
        }
    }


    /**
     * 获取到流转到下一步提示信息
     *
     * @param nextUserName 审批人
     * @return
     */
    private String getTemplate(String nextUserName) {
        String showMessage = "";
        Map<String, String> paramMap = Maps.newHashMap();
        try {
            if (StringUtils.isNotEmpty(nextUserName)) {

                String[] split = nextUserName.split(",");
                for (String nextUse : split) {
                    IUser user = uumsSysUserinfoApi.findByKey(nextUse, IAuthService.KeyType.username, Constants.APP_CODE); //审批人
                    if (user != null) {
                        List<SimplePosition> simplePositionList = new ArrayList(user.getAuthPositions());
                        paramMap.put("companyName", user.getBelongCompanyName());
                        paramMap.put("departmentName", user.getBelongDepartmentName());
                        paramMap.put("trueName", user.getTruename());
                        paramMap.put("positionName", simplePositionList != null ? simplePositionList.get(0).getPositionName() : "");
                        showMessage = MessageEnum.MW000001.getMessage((Map) paramMap);
                    }

                }


            } else {
                showMessage = Constants.MESSAGE_SUCCESS;
            }

        } catch (Exception e) {
            log.debug(e.toString());
        }
        return showMessage;
    }

    /**
     * 审批提交
     *
     * @param applicationForm   表单
     * @param workItemId        活动实例id
     * @param outcome           连线规则
     * @param message           审批意见
     * @param nextUserName      审批人
     * @param location          当前环节
     * @param copyLocation      抄送下一环节
     * @param copyMessage       抄送意见
     * @param copyNextUserNames 抄送人员
     * @param notificationId    待阅id
     * @param source            来源
     * @param currentUserCode   当前用户OA账号
     * @return
     */
    public JsonResponse saveSubmitTask(ApplicationForm applicationForm, String workItemId, String outcome, String message, String nextUserName, String location, String copyLocation, String copyMessage, String copyNextUserNames, String source, String currentUserCode, String notificationId) {
        log.debug("起草接口----------saveSubmitTask---------->" + applicationForm.toString());
        long ret = 0;
        /**准备操作参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/saveSubmitTask";
        String params = "applicationForm=" + applicationForm.toString() + ",workItemId=" + workItemId + ",outcome=" + outcome + ",message=" + message + ",nextUserName=" + nextUserName + ",location=" + location + ",copyLocation=" + copyLocation + ",copyMessage"
                + copyMessage + ",copyNextUserNames=" + copyNextUserNames + ",notificationId=" + notificationId + ",source=" + source + ",userCode=" + currentUserCode;
        operateLog.setInterfaceParam(params);
        try {
            String pmInsId = applicationForm.getPmInsId();
            operateLog.setBussinessKey(pmInsId);
            UsPmInstence pmInstence = usPmInstenceService.findByPmInsId(pmInsId);//获取主单据
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, currentUserCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            /**更新业务表单数据*/
            Boolean flag = true;
            // 起草环节 || 出车地市应急车负责人配合调度 || 战时调度--出车地市应急车负责人配合调度 || (跨市调动 && 省网优应急车调度负责人审核环节 && 同意决策项)  可进行编辑
            if(StrUtil.equals(Constants.ACTIVITY_START, location) || StrUtil.equals(Constants.COORDINATINGDISPATCHING, location)|| StrUtil.equals(Constants.EMERGENCYVEHICLES, location) || (StrUtil.equals(Constants.EMERGENCYVEHICLES_TO_COORDINATINGDISPATCHING, outcome) && StrUtil.equals(Constants.SCHEDULINGCHIEFAUDIT, location) && StrUtil.equals(Constants.PROCESS_TYPE_B, applicationForm.getProcessType()))){
                String guaranteeStartTime = applicationForm.getGuaranteeStartTime();
                String guaranteeEndTime = applicationForm.getGuaranteeEndTime();
                if(StringUtil.isNotEmpty(guaranteeStartTime) && guaranteeStartTime.length() == 10){
                    applicationForm.setGuaranteeStartTime(guaranteeStartTime + " 00:00");
                }
                if(StringUtil.isNotEmpty(guaranteeEndTime) && guaranteeEndTime.length() == 10){
                    applicationForm.setGuaranteeEndTime(guaranteeEndTime + " 23:59");
                }
                flag = updateApplication(applicationForm, pmInsId, location,"end");
            }
            if (!flag) {
                return JsonResponse.fail("更新业务数据失败");
            }

            /**相关流转审批操作**/
            if (pmInstence != null) {
                /**获取用户**/
                IUser user = SecurityUtils.getCurrentUser();
                /**审批流转**/
                if (StringUtils.isNotEmpty(workItemId) && !Constants.STR_NULL.equals(workItemId)) {
                    if ((!"end".equals(outcome) && StringUtils.isNotEmpty(workItemId)) || (!"end2".equals(outcome) && StringUtils.isNotEmpty(workItemId))|| "end".equals(outcome) || "end2".equals(outcome)) {
                        ret = this.processApproval(Long.parseLong(workItemId), user.getUsername(), user.getTruename(), nextUserName, outcome, location, message, pmInstence);
                        if (ret > 0) {
                            if ("end2".equals(outcome)) { //废除归档
                                List<EmergencyEquipment> emmList = emergencyEquipmentService.findByPmInsId(pmInsId);
                                for (EmergencyEquipment item : emmList) {
                                    item.setEndState("1");
                                    emergencyEquipmentService.update(item);
                                }
                            }
                            if ("end".equals(outcome)) {
                                List<EmergencyEquipment> emmList = emergencyEquipmentService.findByPmInsId(pmInsId);
                                for (EmergencyEquipment item : emmList) {
                                    try {
                                        String preferredMobile = user.getPreferredMobile();
                                        String phoneType = "";

                                        if (StringUtils.isNotEmpty(item.getTheDriverPhone())  && item.getTheDriverPhone().contains(preferredMobile)) {
                                            phoneType = item.getTheDriverPhone();
                                        } else if (StringUtils.isNotEmpty(item.getUavStationLeaderPhone())  && item.getUavStationLeaderPhone().contains(preferredMobile)) {
                                            phoneType = item.getUavStationLeaderPhone();
                                        } else if (StringUtils.isNotEmpty(item.getOpeningStationPhone())  && item.getOpeningStationPhone().contains(preferredMobile)) {
                                            phoneType = item.getOpeningStationPhone();
                                        }
                                        if (StringUtils.isNotEmpty(phoneType) && StringUtils.isNotEmpty(item.getArchiveTime())) {
                                            String endTime = item.getArchiveTime();
                                            String nowTime = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm");
                                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
                                            LocalDateTime endTimeLocal = LocalDateTime.parse(endTime, formatter);
                                            LocalDateTime nowTimeLocal = LocalDateTime.parse(nowTime, formatter);
                                            if (endTimeLocal.isAfter(nowTimeLocal)) {
                                                item.setArchiveTime(nowTime);
                                                String archiveTimeUpdateCount = item.getArchiveTimeUpdateCount();
                                                if(StringUtils.isEmpty(archiveTimeUpdateCount)){
                                                    item.setArchiveTimeUpdateCount("1");
                                                    emergencyEquipmentService.update(item);
                                                }
                                            }else{
                                                    item.setArchiveTimeUpdateCount("1");
                                                    emergencyEquipmentService.update(item);
                                            }
                                        }
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                        log.warn("修改归档时间异常: 【{}】", e.getMessage());
                                    }
                                }
                            }


                        }else{
                            operateLog.setErrorMsg("流程流转失败。");
                            return JsonResponse.fail("流程流转失败。");
                        }
                    } else {
                        ret = 0;
                        operateLog.setErrorMsg("审批人不能为空");
                        return JsonResponse.fail("审批人不能为空");
                    }
                    /**审批环节发起抄送  去掉**/
                    if (ret > 0 && StringUtils.isNotEmpty(copyNextUserNames)) {//审批环节发起抄送
                        this.createToRead(workItemId, pmInstence, copyNextUserNames, copyMessage, copyLocation);
                    }
                } else {//抄送通知
                    ret = this.handleToRead(workItemId, pmInstence, copyNextUserNames, copyMessage, copyLocation, notificationId);
                }
            } else {
                operateLog.setErrorMsg("请联系管理员，主数据查找异常！pmInsId = " + pmInsId);
                return JsonResponse.fail("请联系管理员，主数据查找异常！");
            }
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
        } finally {
            operateLogService.saveLog(operateLog);
            /**提醒流转下一步信息**/
            String showMessage = this.getTemplate(nextUserName);
            return ret > 0 ? JsonResponse.success(ret, showMessage) : JsonResponse.fail(ret, Constants.MESSAGE_FAIL);
        }
    }

    /**
     * 更新业务数据
     *
     * @param applicationForm
     * @return
     */
    private Boolean updateApplication(ApplicationForm applicationForm, String pmInstId, String location,String type) {
        Boolean flag = false;
        try {
            updateFileByPmInsId(applicationForm); // 更新附件信息
            // 当前登录人信息
            IUser user = SecurityUtils.getCurrentUser();
            // 获取手机号
            String phoneNumber = user.getPreferredMobile();

            //获取当前起草绑定车辆
            List<EmergencyEquipment> equipmentList = applicationForm.getEquipmentList(); // 大型应急车
            List<EmergencyEquipment> equipmentMoonList = applicationForm.getEquipmentMoonList();  //卫星车
            List<EmergencyEquipment> equipmentUavList = applicationForm.getEquipmentUavList();   //无人机高空站
            List<EmergencyEquipment> equipmentBleList = applicationForm.getEquipmentBleList();   //卫星便携站

            Map<String, Object> map = new HashMap<>();// 应急设备信息
            map.put("carConfiguRation", "");// 应急设备 类型
            map.put("carConfiguRationParent", "");// 应急设备 大类
            map.put("licensePlate", "");// 应急设备 车牌号
            map.put("carCount", 0); // 应急设备 类型 执行过后
            map.put("licenseCount", 0); // 应急设备车牌号 执行过后
            map.put("location", location); // 当前操作环节
            map.put("phoneNumber", phoneNumber); // 手机号当前处理人的手机号

            // 出车地市应急车负责人配合调度 && 战时调度--出车地市应急车负责人配合调度 这两个环节 不删除数据、只修改当前审批人对应的数据
            if(!StrUtil.equals(Constants.COORDINATINGDISPATCHING, location) && !StrUtil.equals(Constants.EMERGENCYVEHICLES, location)){
                iEmergencyEquipmentService.deleteBypmIntId(pmInstId); // 删除旧数据
                log.debug("updateApplication-删除旧数据-成功");
                this.getCarLicensePlate(applicationForm, equipmentList, map,type); // 大型应急车
                log.debug("updateApplication-大型应急车更新数据-成功");
                this.getCarLicensePlate(applicationForm, equipmentMoonList, map,type); // 卫星车
                log.debug("updateApplication-卫星车更新数据-成功");
                this.getCarLicensePlate(applicationForm, equipmentUavList, map,type); // 无人机高空站
                log.debug("updateApplication-无人机高空站更新数据-成功");
                this.getCarLicensePlate(applicationForm, equipmentBleList, map,type); // 卫星便携站
                log.debug("updateApplication-卫星便携站更新数据-成功");

                /**保存表单**/
                applicationForm.setDeviceConfiguration(MapUtil.getStr(map, "carConfiguRation"));
                applicationForm.setDeviceConfigurationParent(MapUtil.getStr(map, "carConfiguRationParent"));
                applicationForm.setLicensePlate(MapUtil.getStr(map, "licensePlate"));

                // 启动流程
                if (StrUtil.isEmpty(applicationForm.getId())) {
                    BelongInfoTool.setBelongCompanyAndDepartment(applicationForm);
                    applicationForm.setOrderNumber(pmInstId);
                    applicationForm.setPmInsId(pmInstId);
                    this.insert(applicationForm); // 保存业务数据
                }else {
                    // 信息变更
                    this.update(applicationForm); // 变更【申请应急设备配置】、【应急车车牌号】
                }
            }else{
                this.getCarLicensePlate(applicationForm, equipmentList, map,type); // 大型应急车
                log.debug("updateApplication-大型应急车更新数据-成功");
                this.getCarLicensePlate(applicationForm, equipmentMoonList, map,type); // 卫星车
                log.debug("updateApplication-卫星车更新数据-成功");
                this.getCarLicensePlate(applicationForm, equipmentUavList, map,type); // 无人机高空站
                log.debug("updateApplication-无人机高空站更新数据-成功");
                this.getCarLicensePlate(applicationForm, equipmentBleList, map,type); // 卫星便携站
                log.debug("updateApplication-卫星便携站更新数据-成功");
            }

            flag = true;

        } catch (Exception e) {
            Exceptions.printException(e);
            flag = false;
        }
        return flag;

    }

    /**
     * 获取应急设备的信息
     * @param equipmentList 设备列表
     */
    private void getCarLicensePlate(ApplicationForm applicationForm, List<EmergencyEquipment> equipmentList, Map<String, Object> map,String type){
        int index = 0; // 第一条数据 获取  类型

        List<EmergencyEquipment> updateModel = new ArrayList<>(); // 负责人配合调度的数据 （只做更新）

        String carConfiguRation = MapUtil.getStr(map, "carConfiguRation"); // 应急设备 类型
        String carConfiguRationParent = MapUtil.getStr(map, "carConfiguRationParent"); // 应急设备 类型
        String licensePlate = MapUtil.getStr(map, "licensePlate"); // 应急设备 车牌号
        String location = MapUtil.getStr(map, "location"); // 当前操作环节
        String phoneNumber = MapUtil.getStr(map, "phoneNumber"); // 手机号当前处理人的手机号
        int carCount = (int)map.get("carCount"); // 应急设备类型 有效数据
        int licenseCount = (int)map.get("licenseCount"); // 应急设备车牌号 有效数据

        boolean insertFalg = true; // 是否进行循环所有中所有的业务处理

        // 出车地市应急车负责人配合调度 || 战时调度--出车地市应急车负责人配合调度 这两个环节 不删除数据、只修改当前审批人对应的数据
        if(StrUtil.equals(Constants.COORDINATINGDISPATCHING, location) || StrUtil.equals(Constants.EMERGENCYVEHICLES, location)){
            insertFalg = false; // 以上两个环节 不对数据进行处理
        }

        //获取保障时间
        String guaranteeStartTime = applicationForm.getGuaranteeStartTime();
        //获取保障结束时间
        String guaranteeEndTime = applicationForm.getGuaranteeEndTime();

        //判断是否为空
        if (ObjectUtil.isNotEmpty(equipmentList)) {
            Iterator<EmergencyEquipment> iterator = equipmentList.iterator();//大型应急车
            if (ObjectUtil.isNotEmpty(iterator)) {
                //获取大型应急车类型和车牌号
                while (iterator.hasNext()) {
                    EmergencyEquipment emergencyEquipment = iterator.next();

                    // 存在 无人机高空站 无车牌号（null）-卫星便携站 无车牌号 （/）
                    if(StrUtil.isNotEmpty(emergencyEquipment.getLicensePlate()) && !StrUtil.equals("/", emergencyEquipment.getLicensePlate())){
                        if(licenseCount < 1){
                            licensePlate = emergencyEquipment.getLicensePlate();
                        }else{
                            licensePlate = licensePlate + "-" + emergencyEquipment.getLicensePlate();
                        }
                        licenseCount ++ ;
                    }

                    if(carCount < 1){ // 其他类型不存在数据时 作为第一个类型 不需要加 -
                        carConfiguRation = emergencyEquipment.getCarConfiguRation();
                        carConfiguRationParent = emergencyEquipment.getCarConfiguRationParent();
                        carCount ++ ;
                    }else{
                        if(index < 1){ // 本类型的第一条数据 获取  类型
                            carConfiguRation = carConfiguRation + "-" + emergencyEquipment.getCarConfiguRation();
                        }
                    }
                    index ++ ;

                    emergencyEquipment.setEeId(applicationForm.getPmInsId());
                    String endTime = emergencyEquipment.getEndTime();
                    //设备开始使用时间
                    String startTime = emergencyEquipment.getStartTime();
                    long betweenDay = 0;
                    if(StrUtil.isEmpty(startTime) && StrUtil.isEmpty(endTime)){
                          emergencyEquipment.setStartTime(guaranteeStartTime);
                          emergencyEquipment.setEndTime(guaranteeEndTime);
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
                        LocalDateTime dateTime1 = LocalDateTime.parse(guaranteeStartTime, formatter);
                        LocalDateTime dateTime2 = LocalDateTime.parse(guaranteeEndTime, formatter);
                         betweenDay = ChronoUnit.MINUTES.between(dateTime1, dateTime2);
                    }else{
                       emergencyEquipment.setStartTime(emergencyEquipment.getStartTime());
                       emergencyEquipment.setEndTime(emergencyEquipment.getEndTime());
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
                        LocalDateTime dateTime1 = LocalDateTime.parse(startTime, formatter);
                        LocalDateTime dateTime2 = LocalDateTime.parse(endTime, formatter);
                        betweenDay = ChronoUnit.MINUTES.between(dateTime1, dateTime2);
                    }
                    //int betweenDay = 0;
                    //if (StrUtil.isEmpty(startTime) && StrUtil.isEmpty(endTime)) {
                    //    emergencyEquipment.setEndTime(guaranteeEndTime);
                    //    emergencyEquipment.setStartTime(guaranteeStartTime);
                    //    //算出时差
                    //    betweenDay = (int) ((DateUtil.between(DateUtil.parse(guaranteeStartTime), DateUtil.parse(guaranteeEndTime), DateUnit.DAY)) * 24) + 24;
                    //} else {
                    //    emergencyEquipment.setStartTime(emergencyEquipment.getStartTime());
                    //    emergencyEquipment.setEndTime(emergencyEquipment.getEndTime());
                    //    //算出时差
                    //    betweenDay = (int) ((DateUtil.between(DateUtil.parse(startTime), DateUtil.parse(endTime), DateUnit.DAY)) * 24) + 24;
                    //}
                    emergencyEquipment.setCarTime((int)betweenDay);
                    //获取保障地市
                    String applicantUnit = applicationForm.getApplicantUnit().replaceAll("分公司", "");
                    if (Constants.PROCESS_TYPE_C.equals(applicationForm.getProcessType())) {
                        applicantUnit = applicationForm.getSpecificCity().replaceAll("分公司", "");
                    }
                    //出车地市
                    emergencyEquipment.setCarCities(applicantUnit);

                    // 责任人配合调度 时 允许更改自己的数据,其他可以编辑的场景 允许修改所有数据
                    if(insertFalg){
                        emergencyEquipment.setId("");
                    }else if(StrUtil.contains(emergencyEquipment.getTheDriverPhone(), phoneNumber)
                            || StrUtil.contains(emergencyEquipment.getUavStationLeaderPhone(), phoneNumber)
                            || StrUtil.contains(emergencyEquipment.getOpeningStationPhone(), phoneNumber)){

                        EmergencyEquipment byId = iEmergencyEquipmentService.findById(emergencyEquipment.getId());

                        if("start".equals(type)){
                            emergencyEquipment.setArchiveTime(emergencyEquipment.getEndTime());
                        }else if ("end".equals(type) && byId != null && StringUtils.isEmpty(byId.getArchiveTimeUpdateCount())){
                            emergencyEquipment.setArchiveTime(emergencyEquipment.getEndTime());
                        }else if ("end".equals(type) && byId == null){
                            emergencyEquipment.setArchiveTime(emergencyEquipment.getEndTime());
                        }
                        iEmergencyEquipmentService.update(emergencyEquipment); // 变更信息
                    }
                }
                // 责任人配合调度 时 允许更改自己的数据,其他可以编辑的场景 允许修改所有数据
                if(insertFalg){
                    for(EmergencyEquipment item :equipmentList){
                        EmergencyEquipment byId = iEmergencyEquipmentService.findById(item.getId());
                        if("start".equals(type)){
                            item.setArchiveTime(item.getEndTime());
                        }else if ("end".equals(type) && byId != null && StringUtils.isEmpty(byId.getArchiveTimeUpdateCount())){
                            item.setArchiveTime(item.getEndTime());
                        }else if ("end".equals(type) && byId == null ){
                            item.setArchiveTime(item.getEndTime());
                        }
                    }
                    iEmergencyEquipmentService.saveAll(equipmentList); // 保存 新数据
                }
            }
        }
        map.put("carConfiguRation", carConfiguRation); // 应急设备 类型 执行过后
        map.put("carConfiguRationParent",carConfiguRationParent);
        map.put("licensePlate", licensePlate); // 应急设备 类型 执行过后
        map.put("carCount", carCount); // 应急设备 类型 执行过后
        map.put("licenseCount", licenseCount); // 应急设备车牌号 执行过后
    }


    /**
     * 创建待阅工作项
     *
     * @param workItemId        工作项id
     * @param usPmInstence      主单据
     * @param copyNextUserNames 待阅人员
     * @param copyMessage       待阅意见
     * @param copyLocation      待阅环节
     */
    private void createToRead(String workItemId, UsPmInstence usPmInstence, String copyNextUserNames, String copyMessage, String copyLocation) throws Exception {
        IUser user = SecurityUtils.getCurrentUser();
        String[] users = copyNextUserNames.split(",");
        WFNotificationInstModel model = new WFNotificationInstModel();
        WfWorkItemModel wfWorkItemModel = null;
        try {
            /**获取当前环节信息**/
            if (StringUtils.isNotEmpty(workItemId) && !Constants.STR_NULL.equals(workItemId)) {
                wfWorkItemModel = (WfWorkItemModel) workItemService.getWorkItemByWorkItemId(Long.parseLong(workItemId));
                model.setProcessDefId(wfWorkItemModel.getProcessDefId());
                model.setProcessDefName(wfWorkItemModel.getProcessDefName());
                model.setProcessInstId(wfWorkItemModel.getProcessInstId());
                model.setActivityInstId(wfWorkItemModel.getActivityInstId());
                model.setActivityInstName(wfWorkItemModel.getActivityInstName());
                model.setWorkItemId(wfWorkItemModel.getWorkItemId());
                model.setWorkItemName(wfWorkItemModel.getWorkItemName());
            } else {
                model = (WFNotificationInstModel) iwfNotificationService.getNotificationByPmInsIdAndRecipient(usPmInstence.getPmInsId(), user.getUsername());
            }
            /**新增待阅信息**/
            if (users.length > 0) {
                for (String str : users) {
                    WFNotificationInstModel wfNotificationInstModel = new WFNotificationInstModel();
                    /*String recipient = str.split("-")[0];
                    String recipientName = str.split("-")[1];*/
                    String[] split = str.split("-");

                    SimpleUser byUsername = uumsSysUserinfoApi.findByUsername(split[0], Constants.APP_CODE);
                    wfNotificationInstModel.setSendUser(user.getUsername());

                    /**------------------------------------------开始发送短信---------------------------------------------------*/
                  /*  //抄送新增触发短信功能
                    Map<String, Object> params = CollectionUtil.newHashMap();
                    params.put("sendUser", split[0]);
                    params.put("itemSubject", usPmInstence.getPmInsTitle());
                    params.put("fromUser", SecurityUtils.getCurrentUser().getTruename());
                    smsTool.sendSMS(params);*/
                    /**------------------------------------------结束发送短信---------------------------------------------------*/

                    wfNotificationInstModel.setSendUserName(user.getTruename());
                    /*wfNotificationInstModel.setRecipient(recipient);
                    wfNotificationInstModel.setRecipientName(recipientName);*/

                    wfNotificationInstModel.setRecipient(split[0]);
                    wfNotificationInstModel.setRecipientName(byUsername.getTruename());


                    wfNotificationInstModel.setContent(copyMessage);
                    wfNotificationInstModel.setProcessDefId(model.getProcessDefId());
                    wfNotificationInstModel.setProcessDefName(model.getProcessDefName());
                    wfNotificationInstModel.setProcessInstId(model.getProcessInstId());
                    wfNotificationInstModel.setActivityInstId(model.getActivityInstId());
                    wfNotificationInstModel.setActivityInstName(model.getActivityInstName());
                    wfNotificationInstModel.setWorkItemId(model.getWorkItemId());
                    wfNotificationInstModel.setWorkItemName(model.getWorkItemName());
                    wfNotificationInstModel.setReceiptTitle(usPmInstence.getPmInsTitle());
                    wfNotificationInstModel.setReceiptCode(usPmInstence.getPmInsId());
                    wfNotificationInstModel.setStatus("0");
                    wfNotificationInstModel.setNextActivityDefId(copyLocation);
                    iwfNotificationService.saveLocalNotification(wfNotificationInstModel);
                    todoBusOperatorService.openTodoToRead(usPmInstence,wfNotificationInstModel.getRecipient(),wfNotificationInstModel.getRecipientName(),wfNotificationInstModel);
                }
            }
        } catch (Exception e) {
            log.debug(e.toString());
        }
    }

    /**
     * 办理待阅
     *
     * @param workItemId        工作项id
     * @param pmInstence        主单据
     * @param copyNextUserNames 待阅人员
     * @param copyMessage       待阅意见
     * @param copyLocation      待阅环节
     * @param notificationId    待阅id
     * @return
     * @throws Exception
     */
    private long handleToRead(String workItemId, UsPmInstence pmInstence, String copyNextUserNames, String copyMessage, String copyLocation, String notificationId) throws Exception {
        long ret = 0;
        WFNotificationInstModel model = (WFNotificationInstModel) iwfNotificationService.getWFNotificationById(notificationId);
        if (copyLocation != null && StringUtils.isNotEmpty(copyNextUserNames)) {//继续通知
            this.createToRead(workItemId, pmInstence, copyNextUserNames, copyMessage, copyLocation);
            if (model != null) {
                ret = iwfNotificationService.updateNotificationStatus(model.getId(), "1", null);
            }
        } else {//结束通知
            if (model != null) {
                ret = iwfNotificationService.updateNotificationStatus(model.getId(), "1", copyMessage);
                todoBusOperatorService.closeTodoDoRead(pmInstence,model);
            }
        }
        return ret;
    }


    /**
     * 流转下一步
     *
     * @param workItemID      活动实例id
     * @param currentUserCode 当前登录人code
     * @param currentUserName 当前登录人姓名
     * @param nextUserName    审批人
     * @param outcome         连线规则
     * @param location        当前所处环节
     * @param message         审批意见
     * @param pmInstence      主单据
     * @return
     */
    @Override
    public long processApproval(Long workItemID, String currentUserCode, String currentUserName, String nextUserName, String outcome, String location, String message, UsPmInstence pmInstence) {
        long ret;
        Map<String, Object> map = new HashMap<>();
        if (nextUserName != null) {
            map.put("inputUserId", nextUserName);//指定下一审批人
            String[] names = nextUserName.split(",");
            if (names.length > 0) {
//                map.put("inputUserIdList", WfParticipantOperator.getParticipantList(names));//指定多工作项用人
                map.put("inputUserIdList", WfParticipantOperator.getParticipantList(names));//指定多工作项用人
            }
        }
        map.put("outcome", outcome);
        map.put("receiptId", pmInstence.getId());
        map.put("title", pmInstence.getPmInsTitle());
        map.put("code", pmInstence.getPmInsId());
        map.put("currentUserCode", currentUserCode);
        map.put("appCode", Constants.APP_CODE);
        try {
            //添加流程审批意见
            workItemService.submitApprovalMsg(workItemID, message);
            //根据工作项ID完成工作项 如果第三个参数为true，则启用事务分割；如果第二个参数为false，则不启用事务分割
            ret = workItemService.finishWorkItemWithRelativeData(workItemID, map, false);
        } catch (Exception e) {
            e.printStackTrace();
            ret = 0;
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return ret;
    }

    /**
     * 根据登录人所在公司获取流程
     *
     * @return
     */
    private Map<String, String> getProcessMap(String processType) {
        Map<String, String> map = Maps.newHashMap();
        switch (processType) {
            case Constants.PROCESS_TYPE_A:
                map.put("processName", Constants.YJTXC_NOABRANCH);
                map.put("processType", "A");
                break;
            case Constants.PROCESS_TYPE_B:
                map.put("processName", Constants.YJTXC_BRANCH);
                map.put("processType", "B");
                break;
            default:
                map.put("processName", Constants.YJTXC_PROVINCE);
                map.put("processType", "C");
                break;
        }
        return map;
    }


    /**
     * 获取申请表单
     *
     * @param processInstId 流程实例id
     * @param workFlag      待办已办标识
     * @param source        来源
     * @param userCode      Oa账户
     * @return
     */
    @Override
    public JsonResponse getFormDetail(Long processInstId, String workFlag, String source, String pmInsId, String location, String userCode) {
        ApplicationForm form = new ApplicationForm();
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/getFormDetail";
        String params = "processInstId=" + processInstId + ",workFlag=" + workFlag + ",source=" + source + ",location=" + location + ",userCode=" + userCode;
        operateLog.setInterfaceParam(params);
        try {
            /**判断来源记录日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            log.debug("~~~~~~~~~~~~~~~~~~~~~~~~~getFormDetail~~~~~~~~~~~~~~~~~~");
            /**点击办理查看计划详情**/
            if (StrUtil.isNotEmpty(pmInsId)) {
                form = applicationFormRepository.getFormDetailByPmInsId(pmInsId);
            }
            List<SysFile> partFile = fileExtendService.getPartFile(pmInsId, "1");
            if (CollectionUtil.isNotEmpty(partFile)) {
                form.setAttachmentList(partFile);
            }


            //回显应急设备
            List<EmergencyEquipment> equipmentListAll = iEmergencyEquipmentService.findByAll(pmInsId);
            List<EmergencyEquipment> equipmentList = iEmergencyEquipmentService.findByPmInsIdC(pmInsId, Constants.BXZ);  //大型应急车
            List<EmergencyEquipment> equipmentMoonList = iEmergencyEquipmentService.findByPmInsIdC(pmInsId, Constants.WXC);  //卫星车
            List<EmergencyEquipment> equipmentUavList = iEmergencyEquipmentService.findByPmInsIdC(pmInsId, Constants.WRJ);  //无人机
            List<EmergencyEquipment> equipmentBleList = iEmergencyEquipmentService.findByPmInsIdC(pmInsId, Constants.YJC);  //卫星便携站

            //所有设备
            if (CollectionUtil.isNotEmpty(equipmentListAll)) {
                for (EmergencyEquipment emergencyEquipment : equipmentListAll) {
                    emergencyEquipment.setUserName(addUserName1(emergencyEquipment.getOpeningStationPhone()));
                }
                form.setEquipmentList(equipmentListAll);
            }

//            //大型应急车
//            if (CollectionUtil.isNotEmpty(equipmentList)) {
//                for (EmergencyEquipment emergencyEquipment : equipmentList) {
//                    emergencyEquipment.setUserName(addUserName1(emergencyEquipment.getOpeningStationPhone()));
//                }
//                form.setEquipmentList(equipmentList);
//            }
//            //卫星车
//            if (CollectionUtil.isNotEmpty(equipmentMoonList)) {
//                for (EmergencyEquipment emergencyEquipment : equipmentMoonList) {
//                    emergencyEquipment.setUserName(addUserName1(emergencyEquipment.getOpeningStationPhone()));
//                }
//                form.setEquipmentMoonList(equipmentMoonList);
//            }
//            //无人机
//            if (CollectionUtil.isNotEmpty(equipmentUavList)) {
//                for (EmergencyEquipment emergencyEquipment : equipmentUavList) {
//                    emergencyEquipment.setUserName(addUserName1(emergencyEquipment.getUavStationLeaderPhone()));
//                }
//                form.setEquipmentUavList(equipmentUavList);
//            }
//            //卫星便携站
//            if (CollectionUtil.isNotEmpty(equipmentBleList)) {
//                for (EmergencyEquipment emergencyEquipment : equipmentBleList) {
//                    emergencyEquipment.setUserName(addUserName1(emergencyEquipment.getUavStationLeaderPhone()));
//                }
//                form.setEquipmentBleList(equipmentBleList);
//            }


        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
            Exceptions.printException(e);
        } finally {
            operateLogService.saveLog(operateLog);
        }
        return JsonResponse.success(form);
    }

    /**
     * 获取决策项
     *
     * @param processInstId  流程实例id
     * @param processDefName 流程定义名称
     * @param location       当前环节
     * @param source         来源
     * @param userCode       当前操作人账号
     * @return
     */
    @Override
    public JsonResponse getDecisions(String processInstId, String processDefName, String location, String source, String userCode, String applyType) {
        /**处理操作参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/getDecisions";
        String params = "processInstId=" + processInstId + ",processDefName=" + processDefName + ",location=" + location + ",source=" + source + ",userCode=" + userCode;
        operateLog.setInterfaceParam(params);
        operateLog.setBussinessKey("processInstId=" + processInstId);
        List<SimpleAppDecision> decisions = Lists.newArrayList();
        Map<String, String> map = Maps.newHashMap();
        try {
            /**判断是否是从手机端还是PC端记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }


            //获取流程类型
            processDefName = getprocessDefName(applyType);
            /**当前环节下所有决策**/
            map.put("appCode", Constants.APP_CODE);
            map.put("processDefId", processDefName);
            map.put("activityDefId", location);
            decisions = uumsSysAppDecisionApi.findDecisions(Constants.APP_CODE, map);
            //正常起草屏蔽"废除归档"决策项
            if (Constants.ACTIVITY_START.equals(location) && StringUtils.isEmpty(processInstId)) {
                Iterator<SimpleAppDecision> it = decisions.iterator();
                while (it.hasNext()) {
                    SimpleAppDecision next = it.next();
                    String decisionId = next.getDecisionId();
                    if (Constants.OUTCOME_END.equals(decisionId)) {
                        it.remove();
                    }
                }
            }
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
            Exceptions.printException(e);
        }
        return JsonResponse.success(decisions);
    }

    /**
     * 获取流程类型
     *
     * @param applyType
     * @return
     */
    private String getprocessDefName(String applyType) {

        //日常应急调度地市内和跨地市都用一个流程
        String processDefName = null;
        if (Constants.PROCESS_TYPE_A.equals(applyType)) {
            processDefName = Constants.YJTXC_NOABRANCH;
        }
        if (Constants.PROCESS_TYPE_B.equals(applyType)) {
            processDefName = Constants.YJTXC_BRANCH;
        }


        if (Constants.PROCESS_TYPE_C.equals(applyType)) {
            processDefName = Constants.YJTXC_PROVINCE;
        }
        return processDefName;
    }

    /**
     * 获取决策项审批人员
     *
     * @param processInstId  流程实例id
     * @param source         来源
     * @param userCode       用户OA账户
     * @param sysAppDecision 决策对象
     * @return
     */
    @Override
    public JsonResponse getOrgAndUser(String processInstId, String source, String userCode, SimpleAppDecision sysAppDecision, String localhost, String phone, String licensePlate, String belongCompanyCodes) {
        JsonResponse userList = new JsonResponse();
        /**准备操作日志参数**/
        SysOperateLog operateLog = new SysOperateLog();
        String param2 = "/getOrgAndUser";
        String params = "processInstId=" + processInstId + ",source=" + source + ",userCode=" + userCode + ",SimpleAppDecision=" + sysAppDecision.toString();
        operateLog.setInterfaceParam(params);
        operateLog.setBussinessKey("processInstId=" + processInstId);

        try {
            /**判断来源记录操作日志**/
            JsonResponse returnObj = operateLogTool.operationSource(source, userCode, param1, param2, operateLog);
            if (returnObj != null) {
                return returnObj;
            }
            IUser currentUser = SecurityUtils.getCurrentUser();
            /*  if ("yjtxc0016".equals(sysAppDecision.getId())||"yjtxc_010".equals(sysAppDecision.getId())) {*/
            if ("yjtxc_010".equals(sysAppDecision.getId())) {
                //根据流程实例ID查到主单据ID
                String pmInsId = applicationFormRepository.findByWorkItemProcessInstId(processInstId);
                ApplicationForm applicationForm = applicationFormRepository.findByPmIndId(pmInsId);
                String deviceConfiguration = applicationForm.getDeviceConfiguration();
                String processType = applicationForm.getProcessType();
                StringBuilder sbCar = new StringBuilder();
                IUser user = null;
                if (Constants.PROCESS_TYPE_B.equals(processType)) {
                    String[] split = phone.split(",");
                    for (String num : split) {
                            user = iuserNoSessionUtil.getIuserByPreferredMobile(num);
                            if (!ObjectUtil.isNotEmpty(user)) {
                                //获取负责人
                                GarageConfiguration garageConfiguration = gerservice.findByPonte(num);
                                String prone = garageConfiguration.getOpeningStationPhone();
                                String[] split1 = prone.split(",");
                                for (String num1 : split1) {
                                    user = iuserNoSessionUtil.getIuserByPreferredMobile(num1);
                                    if (ObjectUtil.isNotEmpty(user) && !sbCar.toString().contains(user.getUsername())) {
                                        sbCar.append(user.getUsername()).append(",");
                                    }
                                }
                        }
/*                        if (ObjectUtil.isNotEmpty(user) && !sbCar.toString().contains(user.getUsername())) {
                            sbCar.append(user.getUsername()).append(",");
                        }*/
                    }
                } else {
                    List<EmergencyEquipment> emergencyEquipmentList = iEmergencyEquipmentService.findByPmInsId(pmInsId);
                    String[] split = phone.split(",");
                    for (String s : split) {
                            user = iuserNoSessionUtil.getIuserByPreferredMobile(s);
                        if (ObjectUtil.isNotEmpty(user) && !sbCar.toString().contains(user.getUsername())) {
                            sbCar.append(user.getUsername()).append(",");
                        }
                    }
                }
                JsonResponse jsonResponse = uumsSysUserinfoApi.findUserByUsernameNoPage(Constants.APP_CODE, sbCar.toString().substring(0, sbCar.lastIndexOf(",")));
                List<Map<String, Object>> data = (List<Map<String, Object>>) jsonResponse.getData();
                List<Map<String, Object>> listusers = (List<Map<String, Object>>) data.get(0).get("user");
                Iterator<Map<String, Object>> iterator1 = listusers.iterator();
                while (iterator1.hasNext()) {
                    Map<String, Object> next = iterator1.next();
                    String id = MapUtil.getStr(next, "id");
                    if (sbCar.toString().contains(id)) {
                        next.put("defaultSelectUser", true);
                        next.put("cancelSelectUser", true);
                    }
                }
                Map<String, Object> map = CollectionUtil.newHashMap();
                map.put("user", data.get(0).get("user"));
                map.put("page", "treeAll");
                map.put("singleSel", false);
                map.put("requSel", false);
                map.put("display", "user");
                map.put("group", "normalGrouping");
                return JsonResponse.success(CollectionUtil.newArrayList(map));
            }
            if ("yjtxc00016".equals(sysAppDecision.getId())) {

                //根据流程实例ID查到主单据ID
                String pmInsId = applicationFormRepository.findByWorkItemProcessInstId(processInstId);
                ApplicationForm applicationForm = applicationFormRepository.findByPmIndId(pmInsId);
                String deviceConfiguration = applicationForm.getDeviceConfiguration();
                String processType = applicationForm.getProcessType();
                StringBuilder sbCar = new StringBuilder();
                IUser user = null;
                IUser user1 = null;
                String getBelongCompanyCode = null;
                String getBelongCompanyCode2 = null;
                if (Constants.PROCESS_TYPE_B.equals(processType)) {
                    String belongCompanycode = currentUser.getBelongCompanyCode();
                    String[] split = phone.split(",");
                    for (String num : split) {
                        GarageConfiguration garageConfiguration1 = gerservice.findByPonte(num);
                        if (ObjectUtil.isNotEmpty(garageConfiguration1)) {

                            user1 = uumsSysUserinfoApi.findByKey(garageConfiguration1.getOpeningStationPhone().trim().replace(" ", ""), IAuthService.KeyType.preferredMobile, Constants.APP_CODE);
                            getBelongCompanyCode = user1.getBelongCompanyCode();
                        } else {
                            user1 = uumsSysUserinfoApi.findByKey(num.trim().replace(" ", ""), IAuthService.KeyType.preferredMobile, Constants.APP_CODE);
                            getBelongCompanyCode = user1.getBelongCompanyCode();
                        }
                        if (belongCompanycode.equals(getBelongCompanyCode)) {

                            if (Constants.WURENJI.equals(deviceConfiguration)) {
                                //获取负责人
                                GarageConfiguration garageConfiguration = gerservice.findByPonte(num);
                                String prone = garageConfiguration.getOpeningStationPhone();
                                String[] split1 = prone.split(",");
                                for (String num1 : split1) {
                                    user = iuserNoSessionUtil.getIuserByPreferredMobile(num1);
                                    if (ObjectUtil.isNotEmpty(user) && !sbCar.toString().contains(user.getUsername())) {
                                        sbCar.append(user.getUsername()).append(",");
                                    }
                                }
                            } else {

                                GarageConfiguration garageConfiguration2 = gerservice.findByPonte(num);
                                if (ObjectUtil.isNotEmpty(garageConfiguration2)) {
                                    user1 = uumsSysUserinfoApi.findByKey(garageConfiguration2.getOpeningStationPhone().trim().replace(" ", ""), IAuthService.KeyType.preferredMobile, Constants.APP_CODE);
                                    getBelongCompanyCode2 = user1.getBelongCompanyCode();
                                } else {
                                    user1 = uumsSysUserinfoApi.findByKey(num.trim().replace(" ", ""), IAuthService.KeyType.preferredMobile, Constants.APP_CODE);
                                    getBelongCompanyCode2 = user1.getBelongCompanyCode();
                                }
                                if (belongCompanycode.equals(getBelongCompanyCode2)) {

                                    //获取负责人
                                    GarageConfiguration garageConfiguration = gerservice.findByPonte(num);
                                    if (!ObjectUtil.isNotEmpty(garageConfiguration)) {
                                        user = uumsSysUserinfoApi.findByKey(num.trim().replace(" ", ""), IAuthService.KeyType.preferredMobile, Constants.APP_CODE);
                                        if (ObjectUtil.isNotEmpty(user) && !sbCar.toString().contains(user.getUsername())) {
                                            sbCar.append(user.getUsername()).append(",");
                                        }
                                    } else {
                                        String prone = garageConfiguration.getOpeningStationPhone();
                                        String[] split1 = prone.split(",");
                                        for (String num1 : split1) {
                                            user = iuserNoSessionUtil.getIuserByPreferredMobile(num1);
                                            if (ObjectUtil.isNotEmpty(user) && !sbCar.toString().contains(user.getUsername())) {
                                                sbCar.append(user.getUsername()).append(",");

                                            }
                                        }

                                    }
                                }
/*                        if (ObjectUtil.isNotEmpty(user) && !sbCar.toString().contains(user.getUsername())) {
                            sbCar.append(user.getUsername()).append(",");
                        }*/
                            }
                        }
                    }

                } else {
                    List<EmergencyEquipment> emergencyEquipmentList = iEmergencyEquipmentService.findByPmInsId(pmInsId);
                    for (EmergencyEquipment emergencyEquipment : emergencyEquipmentList) {

                        if (Constants.WURENJI.equals(deviceConfiguration)) {
                            String[] split = emergencyEquipment.getUavStationLeaderPhone().split(",");
                            user = iuserNoSessionUtil.getIuserByPreferredMobile(split[0]);
                        } else {
                            String phoneNmu = emergencyEquipment.getTheDriverPhone();
                            if (StrUtil.isEmpty(phoneNmu)) {
                                phoneNmu = emergencyEquipment.getUavStationLeaderPhone();
                            }


                            String[] split = phoneNmu.split(",");
                            user = iuserNoSessionUtil.getIuserByPreferredMobile(split[0]);
                            //获取大型应急车车辆负责人
                            if (!ObjectUtil.isNotEmpty(user)) {
                                //获取使用的车辆

                                EmergencyEquipment emergencyEquipment1 = iEmergencyEquipmentService.findddd(phoneNmu, pmInsId);
                                //获取使用车辆名称
                                String vehicle = emergencyEquipment1.getCarConfiguRation();
                                //获取负责人
                                List<GarageConfiguration> garageConfigurationList = gerservice.findByIdddd(phoneNmu, vehicle);
                                GarageConfiguration garageConfiguration = garageConfigurationList.get(0);
                                String prone = garageConfiguration.getOpeningStationPhone();
                                String[] splita = prone.split(",");
                                user = iuserNoSessionUtil.getIuserByPreferredMobile(splita[0]);
                            }
                        }
                        if (ObjectUtil.isNotEmpty(user) && !sbCar.toString().contains(user.getUsername())) {
                            sbCar.append(user.getUsername()).append(",");
                        }
                    }
                }
                if (StrUtil.isNotEmpty(sbCar.toString())) {
                    JsonResponse jsonResponse = uumsSysUserinfoApi.findUserByUsernameNoPage(Constants.APP_CODE, sbCar.toString().substring(0, sbCar.lastIndexOf(",")));
                    List<Map<String, Object>> data = (List<Map<String, Object>>) jsonResponse.getData();
                    List<Map<String, Object>> listusers = (List<Map<String, Object>>) data.get(0).get("user");
                    Iterator<Map<String, Object>> iterator1 = listusers.iterator();
                    while (iterator1.hasNext()) {
                        Map<String, Object> next = iterator1.next();
                        String id = MapUtil.getStr(next, "id");
                        if (sbCar.toString().contains(id)) {
                            next.put("defaultSelectUser", true);
                            next.put("cancelSelectUser", true);
                        }
                    }
                    Map<String, Object> map = CollectionUtil.newHashMap();
                    map.put("user", data.get(0).get("user"));
                    map.put("page", "treeAll");
                    map.put("singleSel", false);
                    map.put("requSel", false);
                    map.put("display", "user");
                    map.put("group", "normalGrouping");
                    return JsonResponse.success(CollectionUtil.newArrayList(map));
                }
                return JsonResponse.builder().errcode(SUCCESS_CODE).timestamp(com.simbest.boot.util.DateUtil.getCurrent())
                        .status(SUCCESS_STATUS).data(CollectionUtil.newArrayList()).build();
            }
            if ("yjtxc0016".equals(sysAppDecision.getId())) {
                //根据流程实例ID查到主单据ID
                String pmInsId = applicationFormRepository.findByWorkItemProcessInstId(processInstId);
                ApplicationForm applicationForm = applicationFormRepository.findByPmIndId(pmInsId);
                String deviceConfiguration = applicationForm.getDeviceConfiguration();
                String processType = applicationForm.getProcessType();
                StringBuilder sbCar = new StringBuilder();
                IUser user = null;
                if (Constants.PROCESS_TYPE_B.equals(processType)) {
                    if (StrUtil.isNotEmpty(phone)) {
                        String[] split = phone.split(",");
                        for (String num : split) {
                            if (StrUtil.isEmpty(num)) {
                                continue;
                            }
                            if (Constants.WURENJI.equals(deviceConfiguration)) {
                                //获取负责人
                                // GarageConfiguration garageConfiguration = gerservice.findByPonte(num);
                                List<GarageConfiguration> garageConfigurationList = gerservice.findByPonteList(num);
                                for (GarageConfiguration garageConfiguration : garageConfigurationList) {
                                    String prone = garageConfiguration.getOpeningStationPhone();
                                    String[] split1 = prone.split(",");
                                    for (String num1 : split1) {
                                        user = iuserNoSessionUtil.getIuserByPreferredMobile(num1);
                                        if (ObjectUtil.isNotEmpty(user) && !sbCar.toString().contains(user.getUsername())) {
                                            sbCar.append(user.getUsername()).append(",");
                                        }
                                    }
                                }

                            } else {
                                //获取负责人
                                // GarageConfiguration garageConfiguration = gerservice.findByPonte(num);
                                List<GarageConfiguration> garageConfigurationList = gerservice.findByPonteList(num);
                                if (garageConfigurationList.size() == 0) {
                                    user = uumsSysUserinfoApi.findByKey(num.trim(), IAuthService.KeyType.preferredMobile, Constants.APP_CODE);
                                    if (user!=null){
                                        sbCar.append(user.getUsername()).append(",");
                                    }
                                } else {
                                    for (GarageConfiguration garageConfiguration : garageConfigurationList) {
                                        if (!ObjectUtil.isNotEmpty(garageConfiguration)) {
                                            user = uumsSysUserinfoApi.findByKey(num.trim().replace(" ", ""), IAuthService.KeyType.preferredMobile, Constants.APP_CODE);
                                            if (ObjectUtil.isNotEmpty(user) && !sbCar.toString().contains(user.getUsername())) {
                                                sbCar.append(user.getUsername()).append(",");
                                            }
                                        } else {
                                            String prone = garageConfiguration.getOpeningStationPhone();
                                            String[] split1 = prone.split(",");
                                            for (String num1 : split1) {
                                                user = iuserNoSessionUtil.getIuserByPreferredMobile(num1);
                                                if (ObjectUtil.isNotEmpty(user) && !sbCar.toString().contains(user.getUsername())) {
                                                    sbCar.append(user.getUsername()).append(",");

                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }


                } else {
                    List<EmergencyEquipment> emergencyEquipmentList = iEmergencyEquipmentService.findByPmInsId(pmInsId);
                    for (EmergencyEquipment emergencyEquipment : emergencyEquipmentList) {

                        if (Constants.WURENJI.equals(deviceConfiguration)) {
                            String[] split = emergencyEquipment.getUavStationLeaderPhone().split(",");
                            user = iuserNoSessionUtil.getIuserByPreferredMobile(split[0]);
                        } else {
                            String phoneNmu = emergencyEquipment.getTheDriverPhone();
                            if (StrUtil.isEmpty(phoneNmu)) {
                                phoneNmu = emergencyEquipment.getUavStationLeaderPhone();
                            }
                            String[] split = phoneNmu.split(",");
                            user = iuserNoSessionUtil.getIuserByPreferredMobile(split[0]);
                            //获取大型应急车车辆负责人
                            if (!ObjectUtil.isNotEmpty(user)) {
                                //获取使用的车辆
                                EmergencyEquipment emergencyEquipment1 = iEmergencyEquipmentService.findddd(phoneNmu, pmInsId);
                                //获取使用车辆名称
                                String vehicle = emergencyEquipment1.getCarConfiguRation();
                                //获取负责人
                                List<GarageConfiguration> garageConfigurationList = gerservice.findByIdddd(phoneNmu, vehicle);
                                GarageConfiguration garageConfiguration = garageConfigurationList.get(0);
                                String prone = garageConfiguration.getOpeningStationPhone();
                                String[] splita = prone.split(",");
                                user = iuserNoSessionUtil.getIuserByPreferredMobile(splita[0]);
                            }
                        }
                        if (ObjectUtil.isNotEmpty(user) && !sbCar.toString().contains(user.getUsername())) {
                            sbCar.append(user.getUsername()).append(",");
                        }
                    }
                }
                if (StrUtil.isNotEmpty(sbCar)) {
                    JsonResponse jsonResponse = uumsSysUserinfoApi.findUserByUsernameNoPage(Constants.APP_CODE, sbCar.toString().substring(0, sbCar.lastIndexOf(",")));
                    List<Map<String, Object>> data = (List<Map<String, Object>>) jsonResponse.getData();
                    List<Map<String, Object>> listusers = (List<Map<String, Object>>) data.get(0).get("user");
                    Iterator<Map<String, Object>> iterator1 = listusers.iterator();
                    while (iterator1.hasNext()) {
                        Map<String, Object> next = iterator1.next();
                        String id = MapUtil.getStr(next, "id");
                        if (sbCar.toString().contains(id)) {
                            next.put("defaultSelectUser", true);
                            next.put("cancelSelectUser", true);
                        }
                    }
                    Map<String, Object> map = CollectionUtil.newHashMap();
                    map.put("user", data.get(0).get("user"));
                    map.put("page", "treeAll");
                    map.put("singleSel", false);
                    map.put("requSel", false);
                    map.put("display", "user");
                    map.put("group", "normalGrouping");
                    return JsonResponse.success(CollectionUtil.newArrayList(map));
                } else {
                    return JsonResponse.builder().errcode(SUCCESS_CODE).timestamp(com.simbest.boot.util.DateUtil.getCurrent())
                            .status(SUCCESS_STATUS).data(CollectionUtil.newArrayList()).build();
                }
            }
//            if ("yjtxc011".equals(sysAppDecision.getId()) || "yjtxc020".equals(sysAppDecision.getId()) || "yjtxc_011".equals(sysAppDecision.getId())) {
//                StringBuilder sbPeople = new StringBuilder();
//                /*     if ("yjtxc011".equals(sysAppDecision.getId())||"yjtxc_011".equals(sysAppDecision.getId())) {*/
//                if ("yjtxc011".equals(sysAppDecision.getId()) || "yjtxc_011".equals(sysAppDecision.getId())) {
//                    String pmInsId = applicationFormRepository.findByWorkItemProcessInstId(processInstId);
//                    ApplicationForm applicationForm = applicationFormRepository.findByapplicationForm(pmInsId);
//                    //获取公司Code
//                    String belongCompanyCode = applicationForm.getBelongCompanyCode();
//                    List<String> peopleList = applicationFormRepository.findByCopyPeople(belongCompanyCode);
//                    Iterator<String> iterator = peopleList.iterator();
//                    while (iterator.hasNext()) {
//                        String userName = iterator.next();
//                        sbPeople.append(userName).append(",");
//                    }
//                    String[] split = new String[0];
//                    IUser user = null;
//                    sbPeople.append(applicationForm.getTrueName()).append(",");
//                    if (StrUtil.isNotEmpty(phone)) {
//                        split = phone.split(",");
//
//                        for (String num : split) {
//                            //获取负责人
//                            //  GarageConfiguration garageConfiguration = gerservice.findByPonte(num);
//
//                            List<GarageConfiguration> garageConfigurationList = gerservice.findByPonteList(num);
//                            // GarageConfiguration garageConfiguration =gerservice.findByPonteLiattion(num,licensePlate);
//                            for (GarageConfiguration garageConfiguration : garageConfigurationList) {
//                                if (garageConfiguration == null) {
//                                    user = uumsSysUserinfoApi.findByKey(num.trim().replace(" ", ""), IAuthService.KeyType.preferredMobile, Constants.APP_CODE);
//                                    sbPeople.append(user.getUsername()).append(",");
//                                } else {
//                                    String prone = garageConfiguration.getOpeningStationPhone();
//                                    String[] split1 = prone.split(",");
//                                    for (String num1 : split1) {
//                                        // user = iuserNoSessionUtil.getIuserByPreferredMobile(num1);
//                                        user = uumsSysUserinfoApi.findByKey(num1.trim().replace(" ", ""), IAuthService.KeyType.preferredMobile, Constants.APP_CODE);
//                                        if (ObjectUtil.isNotEmpty(user) && !sbPeople.toString().contains(user.getUsername())) {
//                                            sbPeople.append(user.getUsername()).append(",");
//                                        }
//                                    }
//                                }
//                            }
//                        }
//                    }
//
//                    if (sysAppDecision.getProcessDefName().equals(Constants.D)) {
//                        List<String> peoList = applicationFormRepository.findByCopyPeopleCode(applicationForm.getPmInsId());
//                        Iterator<String> iteratora = peoList.iterator();
//                        while (iteratora.hasNext()) {
//                            String userName = iteratora.next();
//                            sbPeople.append(userName).append(",");
//                        }
//                    }
//                    //跨地市出出车地市网络部经理
//                    if ("yjtxc011".equals(sysAppDecision.getId())) {
//                        for (String num2 : split) {
//                            List<GarageConfiguration> garageConfigurationList = gerservice.findByPonteList(num2);
//                            if (CollectionUtil.isNotEmpty(garageConfigurationList)) {
//                                for (GarageConfiguration garageConfiguration : garageConfigurationList) {
//                                    if (garageConfiguration != null) {
//                                        String[] splits = garageConfiguration.getOpeningStationPhone().split(",");
//                                        for (String num : splits) {
//                                            /*       user = uumsSysUserinfoApi.findByKey(garageConfiguration.getOpeningStationPhone().replace(" ", ""), IAuthService.KeyType.preferredMobile, Constants.APP_CODE);*/
//                                            user = uumsSysUserinfoApi.findByKey(num.trim().replace(" ", ""), IAuthService.KeyType.preferredMobile, Constants.APP_CODE);
//                                            String usernameW = applicationFormRepository.finBwangLuoBuJingli(user.getBelongCompanyCode());
//                                            if (StringUtils.isNotEmpty(usernameW)) {
//                                                sbPeople.append(usernameW).append(",");
//                                            }
//                                        }
//                                    }
//                                }
//                            } else {
//                                user = uumsSysUserinfoApi.findByKey(num2.trim().replace(" ", ""), IAuthService.KeyType.preferredMobile, Constants.APP_CODE);
//                                String usernameW = applicationFormRepository.finBwangLuoBuJingli(user.getBelongCompanyCode());
//                                sbPeople.append(user.getUsername()).append(",");
//                                if (StringUtils.isNotEmpty(usernameW)) {
//                                    sbPeople.append(usernameW).append(",");
//                                }
//                            }
//                        }
//                    }
//                } else {
//                    IUser user = null;
//                    String[] split = phone.split(",");
//                    for (String num : split) {
//                        user = uumsSysUserinfoApi.findByKey(num.trim().replace(" ", ""), IAuthService.KeyType.preferredMobile, Constants.APP_CODE);
//
//                        if (!ObjectUtil.isNotEmpty(user)) {
//                            //获取负责人
//                            /*   GarageConfiguration garageConfiguration = gerservice.findByPonte(num);*/
//                            //   GarageConfiguration garageConfiguration = gerservice.findByPonteLiattion(num,licensePlate);
//
//                            List<GarageConfiguration> garageConfigurationList = gerservice.findByPonteList(num);
//                            for (GarageConfiguration garageConfiguration : garageConfigurationList) {
//                                String prone = garageConfiguration.getOpeningStationPhone();
//                                String[] split1 = prone.split(",");
//                                for (String num1 : split1) {
//                                    user = uumsSysUserinfoApi.findByKey(num1.trim().replace(" ", ""), IAuthService.KeyType.preferredMobile, Constants.APP_CODE);
//                                    sbPeople.append(user.getUsername()).append(",");
//                                    String belongCompanyCode = user.getBelongCompanyCode();
//                                    List<String> peopleList = applicationFormRepository.findByCopyPeople(belongCompanyCode);
//                                    Iterator<String> iterator = peopleList.iterator();
//                                    while (iterator.hasNext()) {
//                                        String userName = iterator.next();
//                                        sbPeople.append(userName).append(",");
//                                    }
//                                }
//                            }
//                        }
//                        String belongCompanyCode = user.getBelongCompanyCode();
//                        List<String> peopleList = applicationFormRepository.findByCopyPeople(belongCompanyCode);
//                        Iterator<String> iterator = peopleList.iterator();
//                        while (iterator.hasNext()) {
//                            String userName = iterator.next();
//                            sbPeople.append(userName).append(",");
//                        }
//                    }
//                }
//
//                JsonResponse jsonResponse = uumsSysUserinfoApi.findUserByUsernameNoPage(Constants.APP_CODE, sbPeople.toString());
//                List<Map<String, Object>> data = (List<Map<String, Object>>) jsonResponse.getData();
//                List<Map<String, Object>> listusers = (List<Map<String, Object>>) data.get(0).get("user");
//                Iterator<Map<String, Object>> iterator1 = listusers.iterator();
//                while (iterator1.hasNext()) {
//                    Map<String, Object> next = iterator1.next();
//                    if (sbPeople.toString().contains(MapUtil.getStr(next, "id"))) {
//                        next.put("defaultSelectUser", true);
//                        next.put("cancelSelectUser", false);
//                    }
//                }
//                Map<String, Object> map = CollectionUtil.newHashMap();
//                map.put("user", data.get(0).get("user"));
//                map.put("page", "treeAll");
//                map.put("singleSel", false);
//                map.put("requSel", false);
//                map.put("display", "user");
//                map.put("group", "normalGrouping");
//                return JsonResponse.success(CollectionUtil.newArrayList(map));
//            }
            if ("yjtxc019".equals(sysAppDecision.getId())) {
                IUser user = null;
                StringBuilder sbCar = new StringBuilder();
                String[] split = phone.split(",");
                for (String num : split) {
                    user = uumsSysUserinfoApi.findByKey(num.trim(), IAuthService.KeyType.preferredMobile, Constants.APP_CODE);
                    if (!ObjectUtil.isNotEmpty(user)) {
                        //获取负责人

                        //  GarageConfiguration garageConfiguration = gerservice.findByPonteLiattion(num,licensePlate);
                        List<GarageConfiguration> garageConfigurationList = gerservice.findByPonteList(num);
                        for (GarageConfiguration garageConfiguration : garageConfigurationList) {
                            String prone = garageConfiguration.getOpeningStationPhone();
                            String[] split1 = prone.split(",");
                            for (String num1 : split1) {
                                user = uumsSysUserinfoApi.findByKey(num1.trim(), IAuthService.KeyType.preferredMobile, Constants.APP_CODE);
                                if (cn.hutool.core.util.ObjectUtil.isEmpty(user)) continue;
                                if (ObjectUtil.isNotEmpty(user) && !sbCar.toString().contains(user.getUsername())) {
                                    sbCar.append(user.getUsername()).append(",");
                                }
                            }
                        }
                    } else {
                        if (cn.hutool.core.util.ObjectUtil.isEmpty(user)) continue;
                        if (ObjectUtil.isNotEmpty(user) && !sbCar.toString().contains(user.getUsername())) {
                            sbCar.append(user.getUsername()).append(",");
                        }
                    }
                }

                JsonResponse jsonResponse = uumsSysUserinfoApi.findUserByUsernameNoPage(Constants.APP_CODE, sbCar.toString());
                List<Map<String, Object>> data = (List<Map<String, Object>>) jsonResponse.getData();
                List<Map<String, Object>> listusers = (List<Map<String, Object>>) data.get(0).get("user");
                Iterator<Map<String, Object>> iterator1 = listusers.iterator();
                while (iterator1.hasNext()) {
                    Map<String, Object> next = iterator1.next();
                    if (sbCar.toString().contains(MapUtil.getStr(next, "id"))) {
                        next.put("cancelSelectUser", true);
                        next.put("defaultSelectUser", true);
                    }
                }
                Map<String, Object> map = CollectionUtil.newHashMap();
                map.put("user", data.get(0).get("user"));
                map.put("page", "treeAll");
                map.put("singleSel", false);
                map.put("requSel", false);
                map.put("display", "user");
                map.put("group", "normalGrouping");
                return JsonResponse.success(CollectionUtil.newArrayList(map));
            }
            if ("yjtxc010".equals(sysAppDecision.getId())) {
                IUser user = null;
                StringBuilder sbCar = new StringBuilder();
                String[] split = phone.split(",");
                for (String num : split) {
                    user = uumsSysUserinfoApi.findByKey(num.trim(), IAuthService.KeyType.preferredMobile, Constants.APP_CODE);
                    if (!ObjectUtil.isNotEmpty(user)) {
                        //获取负责人
                        GarageConfiguration garageConfiguration = gerservice.findByPonte(num);

                        String prone = garageConfiguration.getOpeningStationPhone();
                        String[] split1 = prone.split(",");

                        for (String prone1 : split1) {
                            user = uumsSysUserinfoApi.findByKey(prone1.trim(), IAuthService.KeyType.preferredMobile, Constants.APP_CODE);
                            String code = user.getBelongDepartmentCode();
                            String username = uumsSysUserinfoApi.findUsernameByOrgAndPosition(user.getUsername(), code, "44", Constants.APP_CODE);
                            user = uumsSysUserinfoApi.findByKey(username, IAuthService.KeyType.username, Constants.APP_CODE);
                            if (cn.hutool.core.util.ObjectUtil.isEmpty(user)) continue;
                            if (ObjectUtil.isNotEmpty(user) && !sbCar.toString().contains(user.getUsername())) {
                                sbCar.append(user.getUsername()).append(",");
                            }
                        }

                    } else {

                        String code = user.getBelongDepartmentCode();
                        String username = uumsSysUserinfoApi.findUsernameByOrgAndPosition(user.getUsername(), code, "44", Constants.APP_CODE);
                        user = uumsSysUserinfoApi.findByKey(username, IAuthService.KeyType.username, Constants.APP_CODE);
                        if (cn.hutool.core.util.ObjectUtil.isEmpty(user)) continue;
                        if (ObjectUtil.isNotEmpty(user) && !sbCar.toString().contains(user.getUsername())) {
                            sbCar.append(user.getUsername()).append(",");
                        }
                    }


                }
                JsonResponse jsonResponse = uumsSysUserinfoApi.findUserByUsernameNoPage(Constants.APP_CODE, sbCar.toString());
                List<Map<String, Object>> data = (List<Map<String, Object>>) jsonResponse.getData();
                List<Map<String, Object>> listusers = (List<Map<String, Object>>) data.get(0).get("user");
                Iterator<Map<String, Object>> iterator1 = listusers.iterator();
                while (iterator1.hasNext()) {
                    Map<String, Object> next = iterator1.next();
                    if (sbCar.toString().contains(MapUtil.getStr(next, "id"))) {
                        next.put("cancelSelectUser", true);
                        next.put("defaultSelectUser", true);
                    }
                }
                Map<String, Object> map = CollectionUtil.newHashMap();
                map.put("user", data.get(0).get("user"));
                map.put("page", "treeAll");
                map.put("singleSel", false);
                map.put("requSel", false);
                map.put("display", "user");
                map.put("group", "normalGrouping");
                return JsonResponse.success(CollectionUtil.newArrayList(map));
            }

            /**根据根据flowType处理查询组织人员**/
            String defaultValue = sysAppDecision.getDecisionConfig();
            if (defaultValue != null) {
                String newDefault = JacksonUtils.unescapeString(defaultValue).replace(("\'"), "\"");
                List<HashMap<String, String>> mapLists = JacksonUtils.json2Type(newDefault, new TypeReference<List<HashMap<String, String>>>() {
                });
                if (mapLists != null && mapLists.size() > 0) {
                    // 只处理 抄送
                    for (HashMap<String, String> hashMap : mapLists) {
                        if(StrUtil.equals("copy", hashMap.get("typeValue"))){
                            String copyUser = "";
                            if(StrUtil.isNotEmpty(processInstId)){
                                String pmInsId = applicationFormRepository.findByWorkItemProcessInstId(processInstId);
                                ApplicationForm applicationForm = applicationFormRepository.findByapplicationForm(pmInsId);

                                // 排除当前审批人
                                if(!StrUtil.equals(applicationForm.getCreator(), currentUser.getUsername())){
                                    copyUser = applicationForm.getCreator();
                                }
                            }

                            // 出车地市负责人经理
                            if(StrUtil.isNotEmpty(belongCompanyCodes)){
                                List<String> companyVehicle = involvesPersonnelConfigurationService.getCompanyVehicle(Arrays.asList(belongCompanyCodes.split(",")));
                                log.debug("ApplicationFormServiceImpl-出车地市负责人经理companyVehicle：【】", companyVehicle.toString());
                                for (String s : companyVehicle) {
                                    if(StrUtil.isNotEmpty(copyUser)){
                                        copyUser = copyUser + "," + s;
                                        continue;
                                    }
                                    copyUser = s;
                                }
                            }
                            // 省公司网优负责人
                            Set<Map<String, Object>> groupExcludeUser = uumsSysUserinfoApi.findUserInfoByGroupSidNoPage(Constants.APP_CODE, "G0707");
                            for (Map<String, Object> map : groupExcludeUser) {
                                // 排除当前审批人
                                if(!StrUtil.equals(MapUtil.getStr(map, "USERNAME"), currentUser.getUsername())){
                                    if(StrUtil.isNotEmpty(copyUser)){
                                        copyUser = copyUser + "," + MapUtil.getStr(map, "USERNAME");
                                        continue;
                                    }
                                    copyUser = MapUtil.getStr(map, "USERNAME");
                                }
                            }
                            log.debug("ApplicationFormServiceImpl-抄送的OA账号：【】", copyUser);
                            JsonResponse jsonResponse = uumsSysUserinfoApi.findUserByUsernameNoPage(Constants.APP_CODE, copyUser);
                            List<Map<String, Object>> data = (List<Map<String, Object>>) jsonResponse.getData();
                            List<Map<String, Object>> listusers = (List<Map<String, Object>>) data.get(0).get("user");
                            Iterator<Map<String, Object>> iterator1 = listusers.iterator();

                            List<String> copyUserList = Arrays.asList(copyUser.split(","));

                            // 赋值 默认选中
                            while (iterator1.hasNext()) {
                                Map<String, Object> next = iterator1.next();
                                if (copyUserList.toString().contains(MapUtil.getStr(next, "id"))) {
                                    next.put("defaultSelectUser", true);
                                    next.put("cancelSelectUser", false);
                                }
                            }
                            Map<String, Object> map = CollectionUtil.newHashMap();
                            map.put("user", data.get(0).get("user"));
                            map.put("page", "treeAll");
                            map.put("singleSel", false);
                            map.put("requSel", false);
                            map.put("display", "user");
                            map.put("group", "normalGrouping");
                            return JsonResponse.success(CollectionUtil.newArrayList(map));
                        }
                    }
                    // 其他 决策项
                    Map<String, String> hashMap = mapLists.get(0);
                    String typeValue = hashMap.get("typeValue");
                    String includeValue = hashMap.get("includeValue");
                    if ("normalStep".equals(typeValue)) {
                        Map<String, String> map = new HashMap<>();
                        map.put("appCode", Constants.APP_CODE);
                        map.put("processDefId", sysAppDecision.getProcessDefId());
                        map.put("activityDefId", sysAppDecision.getActivityDefId());
                        map.put("decisionId", sysAppDecision.getDecisionId());
                        map.put("groupId", sysAppDecision.getGroupId());
                        map.put("decisionConfig", sysAppDecision.getDecisionConfig());
                        userList = uumsSysUserinfoApi.findUserByDecisionNoPage(Constants.APP_CODE, map);

                    }
                    if ("previousStep".equals(typeValue) && includeValue != null) {//退回上一步处理,获取上一步审批人
                        if (processInstId != null) {
                            WfWorkItemModel wfWorkItemModel = (WfWorkItemModel) workItemManager.getByProInstIdAAndAInstId(Long.parseLong(processInstId), includeValue);
                            if (wfWorkItemModel != null) {
                                String userName = wfWorkItemModel.getParticipant();
                                userList = uumsSysUserinfoApi.findUserByUsernameNoPage(Constants.APP_CODE, userName);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            operateLog.setErrorMsg(e.toString());
            Exceptions.printException(e);
            return JsonResponse.defaultErrorResponse();
        }
        return userList;
    }

    /**
     * 查询组织树
     *
     * @return
     */
    @Override
    public JsonResponse queryOrgTree() {
        List<SimpleOrg> simpleOrgList = uumsSysOrgApi.findCityDeapartmentAndCountyCompany(Constants.APP_CODE);
        return JsonResponse.success(simpleOrgList);
    }

    /**
     * 工单编号产生
     *
     * @return
     */
    @Override
    public String creatWorkNumber(String value, String processType) {

        int length = applicationFormRepository.findByDayCount(processType);
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("YYYYMMdd");
        String dateValue = sdf.format(date);
        String number = "001";
        String num = String.valueOf(length + 1);
        if (length == 0) {
            number = value + dateValue + "-" + "001";
        } else if (length > 0 && length < 10) {
            number = value + dateValue + "-" + "00" + num;
        } else if (length >= 10 && length < 100) {
            number = value + dateValue + "-" + "0" + num;
        } else{
            number = value + dateValue + "-" + num;
        }
        //else if (length >= 100 && length < 1000) {
        //
        //}
        return String.valueOf(number);
    }

    /**
     * 根据起草类型查询车辆
     *
     * @param cities
     * @param carType
     * @return
     */
    @Override
    public JsonResponse findByCar(String cities, String carType, String processType) {
        return findByCarWithFilter(cities, carType, processType, null);
    }

    /**
     * 根据起草类型查询车辆（带过滤条件）
     *
     * @param cities
     * @param carType
     * @param processType
     * @param carConfiguRation
     * @return
     */
    public JsonResponse findByCarWithFilter(String cities, String carType, String processType, String carConfiguRation) {
        List<Map<String, Object>> list = null;
        if (!Constants.PROCESS_TYPE_A.equals(processType)) {
            switch (carType) {
                case Constants.WURENJI:  //无人机高空站
                    list = iUavAerialConfigurationStationService.findByDrone();
                    //UAV_STATION_LEADER_PHONE -> 13803710096
                    list = addUserName(list, "UAV_STATION_LEADER_PHONE");
                    break;
                case Constants.WEIXING:  //卫星便携站
                    list = iSatellitePortableLibraryService.findBySatellite();
                    //UAV_STATION_LEADER_PHONE -> 15138805172
                    list = addUserName(list, "UAV_STATION_LEADER_PHONE");
                    break;
                case Constants.DAXINGYINGJICHE:  //大型应急车
                    list = iGarageConfigurationService.findByBigCar();
                    //OPENING_STATION_PHONE
                    list = addUserName(list, "OPENING_STATION_PHONE");
                    break;
            }
        } else {
            switch (carType) {
                case Constants.WURENJI:  //无人机高空站按照地市条件查询
                    list = iUavAerialConfigurationStationService.findByDroneAndCities(cities);
                    //UAV_STATION_LEADER_PHONE -> 15138976851
                    list = addUserName(list, "UAV_STATION_LEADER_PHONE");
                    break;
                case Constants.WEIXING:  //卫星便携站按照地市条件查询
                    list = iSatellitePortableLibraryService.findBySatelliteAndCities(cities);
                    //UAV_STATION_LEADER_PHONE
                    list = addUserName(list, "UAV_STATION_LEADER_PHONE");
                    break;
                case Constants.DAXINGYINGJICHE:  //应急通讯车大类
                    list = iGarageConfigurationService.findByBigCarAndCities(cities);
                    //OPENING_STATION_PHONE
                    list = addUserName(list, "OPENING_STATION_PHONE");
                    break;

            }
        }

        // 如果指定了carConfiguRation过滤条件，则进行模糊查询过滤
        if (StrUtil.isNotEmpty(carConfiguRation) && CollectionUtil.isNotEmpty(list)) {
            list = list.stream()
                    .filter(map -> {
                        String carConfig = MapUtil.getStr(map, "CAR_CONFIGU_RATION");
                        return StrUtil.isNotEmpty(carConfig) && carConfig.contains(carConfiguRation);
                    })
                    .collect(java.util.stream.Collectors.toList());
        }

        return JsonResponse.success(com.simbest.boot.util.MapUtil.formatHumpNameForList(list));
    }

    public List<Map<String, Object>> addUserName(List<Map<String, Object>> resultList, String key) {
        List<Map<String, Object>> list = CollectionUtil.newArrayList();
        resultList.stream().forEach(map -> {
            String phoneList = MapUtil.getStr(map, key);
            if (StrUtil.isNotEmpty(phoneList)) {
                String[] splicPhone = phoneList.split(ApplicationConstants.COMMA);
                Map<String, Object> hashMap = Maps.newHashMap(map);
                String userNames = "";
                for (String phone : splicPhone) {
                    IUser iuser = iuserNoSessionUtil.getIuserByPreferredMobile(phone);
                    if (Optional.ofNullable(iuser).isPresent()) {
                        if(StrUtil.isNotEmpty(userNames)){
                            userNames = iuser.getUsername();
                        }else{
                            userNames = userNames + ApplicationConstants.COMMA + iuser.getUsername();
                        }
                    }
                }
                hashMap.put("userName", userNames);
                list.add(hashMap);
            }
        });
        return list;
    }

    public String addUserName1(String theDriverPhone) {
        StringBuilder stringBuilder = new StringBuilder();
        String userName=null;
        if (StrUtil.isNotEmpty(theDriverPhone)) {
                String[] splicPhone = theDriverPhone.split(ApplicationConstants.COMMA);
                for (String phone : splicPhone) {
                    IUser iuser = iuserNoSessionUtil.getIuserByPreferredMobile(phone);
                    if (Optional.ofNullable(iuser).isPresent()) {
                        stringBuilder.append(iuser.getUsername()).append(ApplicationConstants.COMMA);
                    }else {
                        stringBuilder.append("").append(ApplicationConstants.COMMA);
                    }
                }
                if (StrUtil.isNotEmpty(stringBuilder)){
                    userName = stringBuilder.deleteCharAt(stringBuilder.lastIndexOf(ApplicationConstants.COMMA)).toString();
                }
        }
        return userName;
    }
    /**
     * 工单查询
     *
     * @param page
     * @param size
     * @param params
     * @return
     */
    @Override
    public JsonResponse findByCount(int page, int size, Map<String, Object> params) {
        Page<ApplicationForm> applicationForms = null;
        Page<List<Map<String, Object>>> page1 = null;
        try {
            if (page == 0) {
                page = Integer.parseInt(MapUtil.getStr(params, "page"));
            }
            if (size == 0) {
                size = Integer.parseInt(MapUtil.getStr(params, "page"));
            }

            //获取申请人所在组织
            String applicantUnit = MapUtil.getStr(params, "applicantUnit");
            //获取申请标题
            String orderTitle = MapUtil.getStr(params, "orderTitle");
            //获取工单编号
            String pmInsId = MapUtil.getStr(params, "pmInsId");
            //获取申请人姓名
            String trueName = MapUtil.getStr(params, "trueName");
            //获取流程类型
            String processType = MapUtil.getStr(params, "processType");

            //获取车牌号
            String licensePlate = MapUtil.getStr(params, "licensePlate");
            //获取活动名称
            String nameEvent = MapUtil.getStr(params, "nameEvent");
            //获取保障时间
            String guaranteeStartTime = MapUtil.getStr(params, "guaranteeStartTime");


            Map<String, Object> paramBobys = CollectionUtil.newHashMap();
            StringBuffer baseSql = new StringBuffer("select af.*, to_char(af.created_time, 'yyyy-MM-dd') as createdTime, wm.*,act.current_state " +
                    // StringBuilder baseSql = new StringBuilder("select af.*, to_char(af.created_time, 'yyyy-MM-dd') as createdTime, wm.*,act.current_state " +
                    // StringBuilder baseSql = new StringBuffer("select af.*, to_char(af.created_time, 'yyyy-MM-dd') as createdTime, wm.*,act.current_state " +

                    "  from US_APPLICATION_FORM af, " +
                    "       (select ww.activity_inst_name, " +
                    "               ww.enabled, " +
                    "               ww.receipt_code, " +
                    "               ww.activity_def_id " +
                    "          from WF_WORKITEM_MODEL ww " +
                    "         where ww.work_item_id in " +
                    "               (select t.work_item_id " +
                    "                  from WF_WORKITEM_MODEL t " +
                    "                 where to_char(t.process_inst_id) || t.created_time in " +
                    "                       (select to_char(t.process_inst_id) || " +
                    "                               max(t.created_time) " +
                    "                          from WF_WORKITEM_MODEL t " +
                    "                         group by t.process_inst_id))) wm,ACT_BUSINESS_STATUS act " +
                    " where af.enabled = 1 " +
                    "   and wm.enabled = '1' " +
                    "   and af.pm_ins_id = wm.receipt_code " +
                    "   and act.receipt_code=af.pm_ins_id");

            //判断申请人所在组织
            if (StrUtil.isNotEmpty(applicantUnit)) {
                baseSql.append(" and af.belong_company_name=:applicantUnit");
                paramBobys.put("applicantUnit", applicantUnit);
            }

            if (StrUtil.isNotEmpty(orderTitle)) {
                baseSql.append(" and af.order_title like concat(concat('%', :orderTitle), '%')");
                paramBobys.put("orderTitle", orderTitle);
            }
            if (StrUtil.isNotEmpty(pmInsId)) {
                baseSql.append("   and af.order_number like concat(concat('%', :pmInsId), '%')");
                paramBobys.put("pmInsId", pmInsId);
            }
            if (StrUtil.isNotEmpty(trueName)) {
                baseSql.append("  and af.user_name   like concat(concat('%', :trueName), '%')");
                paramBobys.put("trueName", trueName);
            }
            if (StrUtil.isNotEmpty(processType)) {
                baseSql.append("   and af.process_type=:processType");
                paramBobys.put("processType", processType);
            }

            if (StrUtil.isNotEmpty(licensePlate)) {
                baseSql.append("   and af.license_plate like concat(concat('%', :licensePlate), '%')");
                paramBobys.put("licensePlate", licensePlate);
            }
            if (StrUtil.isNotEmpty(nameEvent)) {
                baseSql.append("  and af.name_event   like concat(concat('%', :nameEvent), '%')");
                paramBobys.put("nameEvent", nameEvent);
            }
            if (StrUtil.isNotEmpty(guaranteeStartTime)) {
                baseSql.append("   and af.guarantee_start_time<=:guaranteeStartTime");
                paramBobys.put("guaranteeStartTime", guaranteeStartTime);
            }
            if (StrUtil.isNotEmpty(guaranteeStartTime)) {
                baseSql.append("   and af.guarantee_end_time>=:guaranteeStartTime");
                paramBobys.put("guaranteeStartTime", guaranteeStartTime);
            }
            if (StrUtil.isNotEmpty(licensePlate)) {
                baseSql.append("  and af.license_plate   like concat(concat('%', :licensePlate), '%')");
                paramBobys.put("licensePlate", licensePlate);
            }

            baseSql.append("   ORDER BY act.create_time desc  ");


            if (!SecurityUtils.getCurrentUserName().equals("wangshaozhe") && !SecurityUtils.getCurrentUserName().equals("zhaochengdong") ) {
                //  判断几级查询权限
                String queryLevel = queryLevelConfigService.findQueryLevel(Constants.SOURCE_PC, SecurityUtils.getCurrentUserName(), Constants.APP_NAME);
                switch (queryLevel) {
                    case DataPermissionConstants.QUERY_LEVEL_FIRST:
                        break;
                    case DataPermissionConstants.QUERY_LEVEL_SECOND:
                        DataPermissionTool.handleSql(baseSql, paramBobys, DataPermissionConstants.QUERY_LEVEL_SECOND);
                        break;
                    case DataPermissionConstants.QUERY_LEVEL_THIRD:
                        DataPermissionTool.handleSql(baseSql, paramBobys, DataPermissionConstants.QUERY_LEVEL_THIRD);
                        break;
                    case DataPermissionConstants.QUERY_LEVEL_FOUR:
                        DataPermissionTool.handleSql(baseSql, paramBobys, DataPermissionConstants.QUERY_LEVEL_FOUR);
                        break;
                    default:
                        baseSql.append(" and t.creator = :username ");
                        paramBobys.put("username", SecurityUtils.getCurrentUserName());
                        break;
                }
            }
            List<Map<String, Object>> relustList = customDynamicWhere.queryNamedParameterForList(baseSql.toString(), params);
            //分页方法
            if (relustList != null && relustList.size() > 0) {
                int relustSize = relustList.size();
                Pageable pageable1 = getPageable(page, size, null, null);
                List<Map<String, Object>> listPart = PageTool.pagination(relustList, page, size);
                page1 = new PageImpl(listPart, pageable1, relustSize);
            }
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return JsonResponse.success(page1);
    }


    public List<Map<String, Object>> findByCount(Map<String, Object> params) {
        List<Map<String, Object>> relustList = null;
        try {
            //获取申请人所在组织
            String applicantUnit = MapUtil.getStr(params, "applicantUnit");
            //获取申请标题
            String orderTitle = MapUtil.getStr(params, "orderTitle");
            //获取工单编号
            String pmInsId = MapUtil.getStr(params, "pmInsId");


            //获取申请人姓名
            String trueName = MapUtil.getStr(params, "trueName");
            //获取流程类型
            String processType = MapUtil.getStr(params, "processType");

            //车牌号
            String licensePlate = MapUtil.getStr(params, "licensePlate");

            //保障时间
            String guaranteeStartTime = MapUtil.getStr(params, "guaranteeStartTime");

            //活动名称
            String nameEvent = MapUtil.getStr(params, "nameEvent");

            Map<String, Object> paramBobys = CollectionUtil.newHashMap();
            StringBuilder baseSql = new StringBuilder("select af.*, to_char(af.created_time, 'yyyy-MM-dd') as createdTime, wm.*,act.current_state " +
                    "  from US_APPLICATION_FORM af, " +
                    "       (select ww.activity_inst_name, " +
                    "               ww.enabled, " +
                    "               ww.receipt_code, " +
                    "               ww.activity_def_id " +
                    "          from WF_WORKITEM_MODEL ww " +
                    "         where ww.work_item_id in " +
                    "               (select t.work_item_id " +
                    "                  from WF_WORKITEM_MODEL t " +
                    "                 where to_char(t.process_inst_id) || t.created_time in " +
                    "                       (select to_char(t.process_inst_id) || " +
                    "                               max(t.created_time) " +
                    "                          from WF_WORKITEM_MODEL t " +
                    "                         group by t.process_inst_id))) wm,ACT_BUSINESS_STATUS act " +
                    " where af.enabled = 1 " +
                    "   and wm.enabled = '1' " +
                    "   and af.pm_ins_id = wm.receipt_code " +
                    "   and act.receipt_code=af.pm_ins_id");

            //判断申请人所在组织
            if (StrUtil.isNotEmpty(applicantUnit)) {
                baseSql.append(" and af.belong_company_name=:applicantUnit");
                paramBobys.put("applicantUnit", applicantUnit);
            }

            if (StrUtil.isNotEmpty(orderTitle)) {
                baseSql.append(" and af.order_title like concat(concat('%', :orderTitle), '%')");
                paramBobys.put("orderTitle", orderTitle);
            }
            if (StrUtil.isNotEmpty(pmInsId)) {
                baseSql.append("   and af.order_number like concat(concat('%', :pmInsId), '%')");
                paramBobys.put("pmInsId", pmInsId);
            }
            if (StrUtil.isNotEmpty(trueName)) {
                baseSql.append("  and af.user_name   like concat(concat('%', :trueName), '%')");
                paramBobys.put("trueName", trueName);
            }
            if (StrUtil.isNotEmpty(processType)) {
                baseSql.append("   and af.process_type=:processType");
                paramBobys.put("processType", processType);
            }

            if (StrUtil.isNotEmpty(licensePlate)) {
                baseSql.append("   and af.order_number like concat(concat('%', :licensePlate), '%')");
                paramBobys.put("licensePlate", licensePlate);
            }
            if (StrUtil.isNotEmpty(guaranteeStartTime)) {
                baseSql.append("   and af.guarantee_start_time<=:guaranteeStartTime");
                paramBobys.put("guaranteeStartTime", guaranteeStartTime);
            }
            if (StrUtil.isNotEmpty(guaranteeStartTime)) {
                baseSql.append("   and af.guarantee_end_time>=:guaranteeStartTime");
                paramBobys.put("guaranteeStartTime", guaranteeStartTime);
            }

            if (StrUtil.isNotEmpty(nameEvent)) {
                baseSql.append("  and af.name_event   like concat(concat('%', :nameEvent), '%')");
                paramBobys.put("nameEvent", nameEvent);
            }

            baseSql.append("   ORDER BY act.create_time desc  ");

            relustList = customDynamicWhere.queryNamedParameterForList(baseSql.toString(), params);
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return relustList;
    }

    /**
     * 根据主单据去查询环节信息
     *
     * @param receiptCode
     * @return
     */
    @Override
    public Map<String, Object> findByWorkItemName(String receiptCode) {
        return applicationFormRepository.findByWorkItemName(receiptCode);
    }

    /**
     * 根据流程实例ID查询环节信息
     *
     * @param processInsId
     * @return
     */
    @Override
    public Map<String, Object> findByWorkItemNameProcessInsId(String processInsId) {
        return applicationFormRepository.findByWorkItemNameProcessInsId(processInsId);
    }

    /**
     * 根据workitemId查询环节信息
     *
     * @param workitemId
     * @return
     */
    @Override
    public Map<String, Object> findByWorkItemNameWorkitemId(String workitemId) {
        return applicationFormRepository.findByWorkItemNameWorkitemId(workitemId);
    }


    /**
     * 工单查询导出
     *
     * @param request
     * @param response
     * @param applicationForm
     */
    @Override
    public void exportOrder(HttpServletRequest request, HttpServletResponse response, ApplicationForm applicationForm) {

        String applicantUnit = applicationForm.getApplicantUnit();

        String orderTitle = applicationForm.getOrderTitle();

        String pmInsId = applicationForm.getPmInsId();


        String licensePlate = applicationForm.getLicensePlate();

        String trueName = applicationForm.getTrueName();

        String processType = applicationForm.getProcessType();


        String nameEvent = applicationForm.getNameEvent(); //活动名称


        String guaranteeStartTime = applicationForm.getGuaranteeStartTime();
        //  String licensePlate = applicationForm.getLicensePlate(); //车牌号


        Map<String, Object> params = CollectionUtil.newHashMap();
        params.put("applicantUnit", applicantUnit);
        params.put("orderTitle", orderTitle);
        params.put("pmInsId", pmInsId);
        params.put("licensePlate", licensePlate);

        params.put("trueName", trueName);
        params.put("processType", processType);

        params.put("guaranteeStartTime", guaranteeStartTime);
        params.put("nameEvent", nameEvent);
        //    params.put("licensePlate", licensePlate);


        String path = request.getServletContext().getRealPath("down");//获取项目动态绝对路径
        LocalDateTime date = LocalDateTime.now();
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String fileName = df.format(date) + "工单信息查询导出全量数据.xls";
        List<Map<String, Object>> formList = this.findByCount(params);
        List<ExportOrder> list = Lists.newArrayList();
        for (Map<String, Object> map : formList) {
            ExportOrder form = creatModel(map);
            list.add(form);
        }
        try {
            String targetFileName = path + "\\" + fileName;
            File targetFile = new File(targetFileName);
            //覆盖文件
            FileUtils.touch(targetFile);
            ExcelUtil<ExportOrder> exportUtil = new ExcelUtil<ExportOrder>(ExportOrder.class);
            exportUtil.exportExcel(list, "工单信息查询导出全量数据", new FileOutputStream(targetFile), null);
            FileTool.download(targetFile.getPath(), response);
        } catch (Exception e) {
            Exceptions.printException(e);
        }
    }

    /**
     * 根据起草类型查询车辆
     *
     * @param params
     * @return
     */
    @Override
    public JsonResponse findByCarTime(Map<String, Object> params) {
        //获取用车地市
        String cities = MapUtil.getStr(params, "cities");
        //获取需要用车类型
        String carType = MapUtil.getStr(params, "carType");
        carType = "" ;
        //获取页面起草流程类型
        String processType = MapUtil.getStr(params, "processType");
        //获取保障开始时间
        String startTime = MapUtil.getStr(params, "startTime");
        //获取保障结束时间
        String endTime = MapUtil.getStr(params, "endTime");
        //获取应急设备类型过滤条件
        String carConfiguRation = MapUtil.getStr(params, "carConfiguRation");
        JsonResponse jsonResponse = findByCarWithFilter(cities, carType, processType, carConfiguRation);
        List<Map<String, Object>> mapList = com.simbest.boot.util.MapUtil.formatHumpNameForList((List<Map<String, Object>>) jsonResponse.getData());
        Iterator<Map<String, Object>> iterator = mapList.iterator();
        while (iterator.hasNext()) {
            Map<String, Object> map = iterator.next();
            //获取所属地市
            String belongCompanyName = MapUtil.getStr(map, "cities");
            String licensePlate = "";
            switch (carType) {
                case Constants.WURENJI:  //无人机高空站
                    licensePlate = MapUtil.getStr(map, "licensePlate");
                    //  licensePlate = "无人机高空站";
                    break;
                case Constants.WEIXING:  //
                    licensePlate = MapUtil.getStr(map, "licensePlate");
                    //licensePlate = "卫星便携站";
                    break;
                default:   //GSM卫星车||大型
                    //获取车牌号
                    licensePlate = MapUtil.getStr(map, "licensePlate");
                    break;
            }
            List<Map<String, Object>> resultsList = iEmergencyEquipmentService.findByCarTime(licensePlate, belongCompanyName, startTime, endTime);
            //查询到就不好调用
            if (CollectionUtil.isNotEmpty(resultsList)) {
                map.put("isShow", false);
                map.put("schedulingCondition", "不可调度");
                String pmInsId = MapUtil.getStr(params, "pmInsId");
                if (StrUtil.isNotEmpty(pmInsId)) {
                    Map<String, Object> map1 = resultsList.get(0);
                    String pmInsId1 = MapUtil.getStr(map1, "EE_ID");
                    if (pmInsId.equals(pmInsId1)) {
                        map.put("isShow", true);
                        map.put("schedulingCondition", "可调度");
                    }
                }
            } else {
                map.put("isShow", true);
            }
        }
        return JsonResponse.success(mapList);
    }

    /**
     * 查询所有车辆类型（不过滤carType）
     *
     * @param params
     * @return
     */
    @Override
    public JsonResponse findAllCarTypes(Map<String, Object> params) {
        //获取用车地市
        String cities = MapUtil.getStr(params, "cities");
        //获取页面起草流程类型
        String processType = MapUtil.getStr(params, "processType");
        //获取保障开始时间
        String startTime = MapUtil.getStr(params, "startTime");
        //获取保障结束时间
        String endTime = MapUtil.getStr(params, "endTime");
        //获取应急设备类型过滤条件
        String carConfiguRation = MapUtil.getStr(params, "carConfiguRation");
        //获取应急设备分类过滤条件
        String carConfiguRationParent = MapUtil.getStr(params, "carConfiguRationParent");

        // 调用新的查询方法，查询所有车辆类型
        JsonResponse jsonResponse = findByCarWithFilterAllTypes(cities, processType, carConfiguRation, carConfiguRationParent);
        List<Map<String, Object>> mapList = com.simbest.boot.util.MapUtil.formatHumpNameForList((List<Map<String, Object>>) jsonResponse.getData());
        Iterator<Map<String, Object>> iterator = mapList.iterator();
        while (iterator.hasNext()) {
            Map<String, Object> map = iterator.next();
            //获取所属地市
            String belongCompanyName = MapUtil.getStr(map, "cities");
            //获取车牌号
            String licensePlate = MapUtil.getStr(map, "licensePlate");

            List<Map<String, Object>> resultsList = iEmergencyEquipmentService.findByCarTime(licensePlate, belongCompanyName, startTime, endTime);
            //查询到就不好调用
            if (CollectionUtil.isNotEmpty(resultsList)) {
                map.put("isShow", false);
                map.put("schedulingCondition", "不可调度");
                String pmInsId = MapUtil.getStr(params, "pmInsId");
                if (StrUtil.isNotEmpty(pmInsId)) {
                    Map<String, Object> map1 = resultsList.get(0);
                    String pmInsId1 = MapUtil.getStr(map1, "EE_ID");
                    if (pmInsId.equals(pmInsId1)) {
                        map.put("isShow", true);
                        map.put("schedulingCondition", "可调度");
                    }
                }
            } else {
                map.put("isShow", true);
            }
        }
        return JsonResponse.success(mapList);
    }

    /**
     * 查询所有车辆类型（不过滤carType）
     *
     * @param cities
     * @param processType
     * @param carConfiguRation
     * @param carConfiguRationParent
     * @return
     */
    public JsonResponse findByCarWithFilterAllTypes(String cities, String processType, String carConfiguRation, String carConfiguRationParent) {
        List<Map<String, Object>> allList = CollectionUtil.newArrayList();

        if (!Constants.PROCESS_TYPE_A.equals(processType)) {
            // 查询所有类型的车辆

            // 大型应急车
            List<Map<String, Object>> bigCarList = iGarageConfigurationService.findByBigCar();
            bigCarList = addUserName(bigCarList, "OPENING_STATION_PHONE");
            if (CollectionUtil.isNotEmpty(bigCarList)) {
                allList.addAll(bigCarList);
            }

        } else {
            // A类型流程：按照地市条件查询所有类型

            // 应急通讯车大类按照地市条件查询
            List<Map<String, Object>> bigCarList = iGarageConfigurationService.findByBigCarAndCities(cities);
            bigCarList = addUserName(bigCarList, "OPENING_STATION_PHONE");
            if (CollectionUtil.isNotEmpty(bigCarList)) {
                allList.addAll(bigCarList);
            }

        }

        // 如果指定了carConfiguRation过滤条件，则进行模糊查询过滤
        if (StrUtil.isNotEmpty(carConfiguRation) && CollectionUtil.isNotEmpty(allList)) {
            allList = allList.stream()
                    .filter(map -> {
                        String carConfig = MapUtil.getStr(map, "CAR_CONFIGU_RATION");
                        return StrUtil.isNotEmpty(carConfig) && carConfig.contains(carConfiguRation);
                    })
                    .collect(java.util.stream.Collectors.toList());
        }

        // 如果指定了carConfiguRationParent过滤条件，支持逗号分割的多个值进行精确匹配
        if (StrUtil.isNotEmpty(carConfiguRationParent) && CollectionUtil.isNotEmpty(allList)) {
            // 按逗号分割应急设备分类参数
            String[] parentTypes = carConfiguRationParent.split(",");
            Set<String> parentTypeSet = new HashSet<>();
            for (String parentType : parentTypes) {
                if (StrUtil.isNotEmpty(parentType.trim())) {
                    parentTypeSet.add(parentType.trim());
                }
            }

            if (!parentTypeSet.isEmpty()) {
                allList = allList.stream()
                        .filter(map -> {
                            String carConfigParent = MapUtil.getStr(map, "CAR_CONFIGU_RATION_PARENT");
                            return StrUtil.isNotEmpty(carConfigParent) && parentTypeSet.contains(carConfigParent);
                        })
                        .collect(java.util.stream.Collectors.toList());
            }
        }

        return JsonResponse.success(com.simbest.boot.util.MapUtil.formatHumpNameForList(allList));
    }


    private ExportOrder creatModel(Map<String, Object> map) {
        ExportOrder exportOrder = new ExportOrder();
        Map<String, Object> params = com.simbest.boot.util.MapUtil.formatHumpName(map);
        //获取工单标题
        String orderTitle = MapUtil.getStr(params, "orderTitle");
        //获取工单编号
        String pmInsId = MapUtil.getStr(params, "pmInsId");
        //获取流程类型
        String processType = MapUtil.getStr(params, "processType");
        switch (processType) {
            case Constants.PROCESS_TYPE_A:
                processType = Constants.A;
                break;
            case Constants.PROCESS_TYPE_B:
                processType = Constants.B;
                break;
            default:
                processType = Constants.C;
                break;
        }


        //申请应急设备配置
        String deviceConfiguration = MapUtil.getStr(params, "deviceConfiguration");
        //车牌号
        String licensePlate = MapUtil.getStr(params, "licensePlate");
        //活动名称
        String nameEvent = MapUtil.getStr(params, "nameEvent");
        //保障开始时间
        String guaranteeStartTime = MapUtil.getStr(params, "guaranteeStartTime");
        //保障结束时间
        String guaranteeEndTime = MapUtil.getStr(params, "guaranteeEndTime");

        //获取申请组织
        String belongCompanyName = MapUtil.getStr(params, "belongCompanyName");
        //获取申请发起时间
        String createdTime = MapUtil.getStr(params, "createdTime").split(" ")[0];
        //获取当前环节
        String activityInstName = MapUtil.getStr(params, "activityInstName");
        //获取工单发起人
        String userName = MapUtil.getStr(params, "userName");
        exportOrder.setAssignedBy(pmInsId);
        exportOrder.setOrganizerName(activityInstName);
        exportOrder.setLeadershipUserName(userName);
        exportOrder.setTheOrganizerName(createdTime);

        exportOrder.setDeviceConfiguration(deviceConfiguration);
        exportOrder.setLicensePlate(licensePlate);
        exportOrder.setNameEvent(nameEvent);
        exportOrder.setGuaranteeStartTime(guaranteeStartTime);
        exportOrder.setGuaranteeEndTime(guaranteeEndTime);

        exportOrder.setApplyOrganization(belongCompanyName);
        exportOrder.setOrderTitle(orderTitle);
        exportOrder.setSourceOfMatters(processType);
        return exportOrder;
    }


    /**
     * 工单查询
     *
     * @return
     */
    public List<Map<String, Object>> findByCount() {
        try {
            StringBuilder baseSql = new StringBuilder("select af.*, wm.* " +
                    "  from US_APPLICATION_FORM af," +
                    "       (select ww.activity_inst_name," +
                    "               ww.enabled," +
                    "               ww.receipt_code," +
                    "               ww.activity_def_id" +
                    "          from WF_WORKITEM_MODEL ww" +
                    "         where ww.work_item_id in" +
                    "               (select t.work_item_id" +
                    "                  from WF_WORKITEM_MODEL t" +
                    "                 where to_char(t.process_inst_id) || t.created_time in" +
                    "                       (select to_char(t.process_inst_id) || max(t.created_time)" +
                    "                          from WF_WORKITEM_MODEL t" +
                    "                         group by t.process_inst_id))) wm" +
                    "  where af.enabled = 1" +
                    "   and wm.enabled = '1'" +
                    "   and af.pm_ins_id = wm.receipt_code");

            List<Map<String, Object>> relustList = customDynamicWhere.queryForList(baseSql.toString());
            return relustList;
        } catch (Exception e) {
            Exceptions.printException(e);
        }
        return null;
    }

    /**
     * 根据id查询
     *
     * @param pmInsId
     * @return
     */
    @Override
    public ApplicationForm getFormDetailByPmInsId(String pmInsId) {
        return applicationFormRepository.getFormDetailByPmInsId(pmInsId);

    }

    /**
     * 核销
     *
     * @param processInstId
     * @param applicationForm
     * @param pmInsId
     * @return
     */
    @Override
    public JsonResponse deleteProcess(Long processInstId, ApplicationForm applicationForm, String pmInsId) {
        if (applicationForm.getId() == null && pmInsId == null) {
            return JsonResponse.fail(-1, Constants.MESSAGE_FAIL);
        } else {
            try {
                //注销都是把相应表的enabled变为0
                //牵涉到
                // Sys_file、US_APPROVAL_FORM、US_PM_INSTENCE、WF_OPTMSG_MODEL、
                // WF_PROCESS_INST_MODEL、WF_WORKITEM_MODEL
                // 这六个表，现在对sys_file这个表没有进行处理，其他进行逻辑删除处理。
                UsPmInstence pmInstence = usPmInstenceService.findByPmInsId(pmInsId);
                if (pmInstence != null && cn.hutool.core.util.ObjectUtil.isNotEmpty(processInstId)) {
                    actBusinessStatusService.updateActBusDataByProInsId(processInstId);
                    //删除流程实例，审批意见，工作项
                    wfOptMsgService.deleteLocalDataByProInsId(processInstId);
                    workItemManager.deleteByProInsId(processInstId);
                    processInstanceService.deleteLocalDataByProInsId(processInstId);
                    //删除流程实例名称  BPS引擎操作
                    processInstanceService.deleteProcessInstance(processInstId);
                    usPmInstenceService.deleteByPmIdHexiao(pmInstence.getId());
                } else if (pmInstence != null) {
                    usPmInstenceService.deleteByPmIdHexiao(pmInstence.getId());
                }
//                applicationFormRepository.deleteByFromId(application.getId(), LocalDateTime.now());
                applicationFormRepository.deleteByFromId(applicationForm.getId());
            } catch (Exception e) {
                Exceptions.printException(e);
                return JsonResponse.fail(-1, Constants.MESSAGE_FAIL);
            }
            log.debug("流程processInstId" + processInstId + "注销了流程。注销人为" + SecurityUtils.getCurrentUserName());
        }
        return JsonResponse.success(Constants.MESSAGE_SUCCESS);
    }

    /**
     * 手动核销统一代办
     *
     * @param processInstId
     * @param applicationForm
     */
    @Override
    public void deleteNtodoDate(Long processInstId, ApplicationForm applicationForm) {
        Map<String, Object> babyParams = applicationFormRepository.findByActVusiness(processInstId);
        String businesskey = MapUtil.getStr(babyParams, "BUSINESS_KEY");
        String receipttitle = MapUtil.getStr(babyParams, "RECEIPT_TITLE");
        String processinstid = MapUtil.getStr(babyParams, "PROCESS_INST_ID");
        String workitemid = MapUtil.getStr(babyParams, "WORK_ITEM_ID");
        ActBusinessStatus actBusinessStatus = new ActBusinessStatus();
        actBusinessStatus.setBusinessKey(businesskey);
        actBusinessStatus.setProcessInstId(Long.parseLong(processinstid));
        actBusinessStatus.setWorkItemId(Long.parseLong(workitemid));
        actBusinessStatus.setReceiptTitle(receipttitle);
        todoBusOperatorService.closeTodo(actBusinessStatus, SecurityUtils.getCurrentUser().getUsername());
    }

    /**
     * 根据主单据查询流程类型
     *
     * @param receiptCode
     * @return
     */
    @Override
    public String findByApplicationForm(String receiptCode) {
        return applicationFormRepository.findByApplicationForm(receiptCode);
    }

    /**
     * 模糊查询
     *
     * @param receiptCode
     * @param nameEvent
     * @param licensePlate
     * @return
     */
    @Override
    public ApplicationForm findAallFormDetailByPmInsId(String receiptCode, String nameEvent, String licensePlate) {
        return applicationFormRepository.findAllFormDetailByPmInsId(receiptCode, nameEvent, licensePlate);
    }


}



